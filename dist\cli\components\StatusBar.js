"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusBar = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const ink_1 = require("ink");
const StatusBar = ({ provider, model, theme, messageCount, isTyping }) => {
    const getCurrentTime = () => {
        return new Date().toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    };
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { borderStyle: "single", borderColor: theme.colors.muted, paddingX: 1, justifyContent: "space-between", alignItems: "center", children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { alignItems: "center", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.primary, bold: true, children: ["\uD83D\uDD17 ", provider.toUpperCase()] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.muted, children: "\u2022" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: model })] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { alignItems: "center", children: isTyping ? ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.warning, children: "\u23F3 AI is responding..." })) : ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.success, children: "\u2705 Ready" })) }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { alignItems: "center", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.muted, children: ["\uD83D\uDCAC ", messageCount, " messages"] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.muted, children: "\u2022" }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.muted, children: ["\uD83D\uDD52 ", getCurrentTime()] })] })] }));
};
exports.StatusBar = StatusBar;
//# sourceMappingURL=StatusBar.js.map