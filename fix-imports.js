import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';

function fixImportsInFile(filePath) {
  const content = readFileSync(filePath, 'utf8');
  
  // Fix relative imports to include .js extension
  const fixedContent = content.replace(
    /import\s+.*?\s+from\s+['"](\.[^'"]*?)['"];?/g,
    (match, importPath) => {
      // Skip if already has extension or is external module
      if (importPath.includes('.') && !importPath.endsWith('/')) {
        return match;
      }
      // Add .js extension
      const newPath = importPath.endsWith('/') ? importPath + 'index.js' : importPath + '.js';
      return match.replace(importPath, newPath);
    }
  );
  
  if (content !== fixedContent) {
    writeFileSync(filePath, fixedContent, 'utf8');
    console.log(`Fixed imports in: ${filePath}`);
  }
}

function processDirectory(dirPath) {
  const items = readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = join(dirPath, item);
    const stat = statSync(fullPath);
    
    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else if (extname(item) === '.ts' || extname(item) === '.tsx') {
      fixImportsInFile(fullPath);
    }
  }
}

console.log('Fixing import paths for ES modules...');
processDirectory('./src');
console.log('Done!');
