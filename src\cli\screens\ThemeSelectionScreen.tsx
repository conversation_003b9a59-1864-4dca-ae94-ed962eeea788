/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Header } from '../components/Header.js';
import { ThemePreview } from '../components/ThemePreview.js';
import { THEMES } from '../../core/config/themes.js';
import { Theme } from '../../core/types/index.js';

interface ThemeSelectionScreenProps {
  currentTheme: string;
  onThemeSelected: (themeId: string) => void;
  onError: (error: string) => void;
}

export const ThemeSelectionScreen: React.FC<ThemeSelectionScreenProps> = ({
  currentTheme,
  onThemeSelected,
  onError
}) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [previewTheme, setPreviewTheme] = useState<Theme>(THEMES[0]);

  useEffect(() => {
    // Find current theme index
    const currentIndex = THEMES.findIndex(theme => theme.id === currentTheme);
    if (currentIndex !== -1) {
      setSelectedIndex(currentIndex);
      setPreviewTheme(THEMES[currentIndex]);
    }
  }, [currentTheme]);

  useEffect(() => {
    setPreviewTheme(THEMES[selectedIndex]);
  }, [selectedIndex]);

  useInput((input, key) => {
    if (key.upArrow) {
      setSelectedIndex(prev => (prev > 0 ? prev - 1 : THEMES.length - 1));
    } else if (key.downArrow) {
      setSelectedIndex(prev => (prev < THEMES.length - 1 ? prev + 1 : 0));
    } else if (key.return) {
      onThemeSelected(THEMES[selectedIndex].id);
    } else if (key.escape) {
      // Skip theme selection and use current theme
      onThemeSelected(currentTheme);
    }
  });

  return (
    <Box flexDirection="column" padding={2}>
      <Header 
        title="THEME SELECTION"
        subtitle="Choose your terminal appearance"
        theme={previewTheme}
        showLogo={false}
      />
      
      <Box marginTop={2} flexDirection="column">
        <Text color={previewTheme.colors.accent} bold>
          🎨 Available Themes ({selectedIndex + 1}/{THEMES.length})
        </Text>
        
        <Box marginBottom={2}>
          <Text color={previewTheme.colors.text}>
            Use ↑↓ arrows to preview themes, Enter to select, Esc to skip
          </Text>
        </Box>

        {/* Live theme preview */}
        <Box marginBottom={2}>
          <ThemePreview 
            theme={previewTheme} 
            isSelected={true}
            isActive={true}
          />
        </Box>

        {/* Theme list */}
        <Box flexDirection="column">
          {THEMES.map((theme, index) => (
            <Box key={theme.id} marginBottom={1}>
              <Text 
                color={index === selectedIndex ? previewTheme.colors.accent : previewTheme.colors.muted}
                bold={index === selectedIndex}
              >
                {index === selectedIndex ? '▶ ' : '  '}
                {theme.name}
                {theme.id === currentTheme ? ' (current)' : ''}
              </Text>
              <Text color={previewTheme.colors.muted} dimColor>
                {theme.description}
              </Text>
            </Box>
          ))}
        </Box>

        {/* Instructions */}
        <Box marginTop={2} borderStyle="round" borderColor={previewTheme.colors.primary} padding={1}>
          <Box flexDirection="column">
            <Text color={previewTheme.colors.text} bold>
              🎯 Theme Preview Features:
            </Text>
            <Text color={previewTheme.colors.text}>
              • Live color palette visualization
            </Text>
            <Text color={previewTheme.colors.text}>
              • Sample chat interface preview
            </Text>
            <Text color={previewTheme.colors.text}>
              • Real-time theme switching
            </Text>
            <Text color={previewTheme.colors.text}>
              • Customizable appearance
            </Text>
          </Box>
        </Box>

        <Box marginTop={2}>
          <Text color={previewTheme.colors.warning}>
            💡 You can change themes anytime during chat with /theme command
          </Text>
        </Box>
      </Box>
    </Box>
  );
};
