/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { systemFunctions } from './system.js';
import { webFunctions } from './web.js';
import { fileFunctions } from './file.js';
import { utilityFunctions } from './utility.js';
export class FunctionManager {
    functions = new Map();
    constructor() {
        this.registerDefaultFunctions();
    }
    registerDefaultFunctions() {
        // Register all built-in function categories
        [...systemFunctions, ...webFunctions, ...fileFunctions, ...utilityFunctions].forEach(func => {
            this.functions.set(func.name, func);
        });
    }
    registerFunction(func) {
        this.functions.set(func.name, func);
    }
    unregisterFunction(name) {
        this.functions.delete(name);
    }
    getFunction(name) {
        return this.functions.get(name);
    }
    getAllFunctions() {
        return Array.from(this.functions.values());
    }
    getFunctionsByCategory(category) {
        return this.getAllFunctions().filter(func => func.description.toLowerCase().includes(category.toLowerCase()));
    }
    async executeFunction(name, args) {
        const func = this.getFunction(name);
        if (!func) {
            throw new Error(`Function '${name}' not found`);
        }
        try {
            return await func.handler(args);
        }
        catch (error) {
            throw new Error(`Function '${name}' execution failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    validateFunctionCall(name, args) {
        const func = this.getFunction(name);
        if (!func) {
            return { valid: false, error: `Function '${name}' not found` };
        }
        // Check required parameters
        const required = func.parameters.required || [];
        for (const param of required) {
            if (!(param in args)) {
                return { valid: false, error: `Missing required parameter: ${param}` };
            }
        }
        // Basic type validation
        const properties = func.parameters.properties || {};
        for (const [key, value] of Object.entries(args)) {
            const propSchema = properties[key];
            if (propSchema) {
                const expectedType = propSchema.type;
                const actualType = typeof value;
                if (expectedType === 'string' && actualType !== 'string') {
                    return { valid: false, error: `Parameter '${key}' must be a string` };
                }
                if (expectedType === 'number' && actualType !== 'number') {
                    return { valid: false, error: `Parameter '${key}' must be a number` };
                }
                if (expectedType === 'boolean' && actualType !== 'boolean') {
                    return { valid: false, error: `Parameter '${key}' must be a boolean` };
                }
                if (expectedType === 'array' && !Array.isArray(value)) {
                    return { valid: false, error: `Parameter '${key}' must be an array` };
                }
            }
        }
        return { valid: true };
    }
}
export const functionManager = new FunctionManager();
export * from './system.js';
export * from './web.js';
export * from './file.js';
export * from './utility.js';
//# sourceMappingURL=index.js.map