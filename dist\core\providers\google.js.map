{"version": 3, "file": "google.js", "sourceRoot": "", "sources": ["../../../src/core/providers/google.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC3D,OAAO,EAAE,eAAe,EAAqB,MAAM,WAAW,CAAC;AAG/D,MAAM,OAAO,cAAe,SAAQ,eAAe;IACzC,MAAM,CAAqB;IAEnC,YAAY,MAAyB;QACnC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAkB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAI,YAAY;QACd,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;YAC5E,MAAM,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,KAAa,EACb,OAKC;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;YACrD,KAAK;YACL,gBAAgB,EAAE;gBAChB,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;gBACxC,eAAe,EAAE,OAAO,EAAE,SAAS;aACpC;YACD,iBAAiB,EAAE,OAAO,EAAE,YAAY;SACzC,CAAC,CAAC;QAEH,sCAAsC;QACtC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAChD,IAAI,EAAE,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;YACjD,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC;SAC/B,CAAC,CAAC,CAAC;QAEJ,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,IAAI,CAAC;YACT,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACvB,IAAI,GAAG,eAAe,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;YAChD,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;YACrC,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC3D,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YAEjC,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChC,MAAM,aAAa,GAAmB,EAAE,CAAC;YAEzC,sCAAsC;YACtC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC;YACxD,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,KAAK,MAAM,YAAY,IAAI,sBAAsB,EAAE,CAAC;oBAClD,MAAM,IAAI,GAAiB;wBACzB,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;wBACrB,IAAI,EAAE,YAAY,CAAC,IAAI;wBACvB,SAAS,EAAE,YAAY,CAAC,IAAI;qBAC7B,CAAC;oBAEF,IAAI,CAAC;wBACH,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBACrD,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,IAAI,CAAC,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACtE,CAAC;oBAED,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3B,CAAC;YACH,CAAC;YAED,0EAA0E;YAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACjF,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAEnD,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,KAAK,EAAE;oBACL,WAAW;oBACX,YAAY;oBACZ,WAAW,EAAE,WAAW,GAAG,YAAY;iBACxC;gBACD,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,UAAU;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;CACF"}