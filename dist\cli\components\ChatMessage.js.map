{"version": 3, "file": "ChatMessage.js", "sourceRoot": "", "sources": ["../../../src/cli/components/ChatMessage.tsx"], "names": [], "mappings": ";;;;AAOA,6BAAgC;AAQzB,MAAM,WAAW,GAA+B,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE;IAC5E,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;IACvC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC;IAE3C,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC/B,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;IAExE,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IACxD,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;IAE7D,MAAM,eAAe,GAAG,CAAC,SAAe,EAAE,EAAE;QAC1C,OAAO,SAAS,CAAC,kBAAkB,CAAC,OAAO,EAAE;YAC3C,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAAC,aAA6B,EAAE,EAAE;QAC5D,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;QAE9D,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,CAAC,aACtC,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,mDAEhC,EACN,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAClC,uBAAC,SAAG,IAAa,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YAC1C,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,8BAC1B,IAAI,CAAC,IAAI,OAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SACjD,EAEN,IAAI,CAAC,MAAM,IAAI,CACd,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,YAC9B,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,gCACpB,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAClG,GACH,CACP,EAEA,IAAI,CAAC,KAAK,IAAI,CACb,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,YAC9B,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,+BACnB,IAAI,CAAC,KAAK,IACf,GACH,CACP,IACG,IArBE,KAAK,CAsBT,CACP,CAAC,IACE,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,GAAG,EAAE;QAC5B,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM;YAAE,OAAO,IAAI,CAAC;QAE3C,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAE3E,OAAO,CACL,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,4CAC3B,WAAW,YAAQ,YAAY,aAAS,WAAW,cAC1D,GACH,CACP,CAAC;IACJ,CAAC,CAAC;IAEF,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aAEzC,wBAAC,SAAG,IAAC,UAAU,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACtC,wBAAC,UAAI,IAAC,KAAK,EAAE,SAAS,EAAE,IAAI,mBACzB,QAAQ,OAAG,QAAQ,IACf,EACP,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,YAC5B,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,GAC9B,EACN,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAC1B,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,wBAC1B,OAAO,CAAC,QAAQ,CAAC,KAAK,IACpB,CACR,IACG,EAGN,uBAAC,SAAG,IACF,WAAW,EAAC,OAAO,EACnB,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAChE,OAAO,EAAE,CAAC,EACV,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1B,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAE3B,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,YAC3B,OAAO,CAAC,OAAO,GACX,EAGN,OAAO,CAAC,QAAQ,EAAE,aAAa,IAAI,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAGtF,CAAC,MAAM,IAAI,gBAAgB,EAAE,IAC1B,GACF,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AA5GW,QAAA,WAAW,eA4GtB"}