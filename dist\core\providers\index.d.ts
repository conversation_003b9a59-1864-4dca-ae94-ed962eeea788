/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { BaseLLMProvider } from './base.js';
import { AuthConfig } from '../types/index.js';
export declare class ProviderManager {
    private providers;
    createProvider(providerId: string, config: AuthConfig): BaseLLMProvider;
    getProvider(providerId: string): BaseLLMProvider | undefined;
    hasProvider(providerId: string): boolean;
    removeProvider(providerId: string): void;
    getAllProviders(): Map<string, BaseLLMProvider>;
    validateProvider(providerId: string): Promise<boolean>;
}
export declare const providerManager: ProviderManager;
export * from './base.js';
export * from './openai.js';
export * from './anthropic.js';
export * from './google.js';
export * from './deepseek.js';
//# sourceMappingURL=index.d.ts.map