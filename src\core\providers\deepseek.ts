/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import axios, { AxiosInstance } from 'axios';
import { BaseLL<PERSON>rovider, LLMProviderConfig } from './base.js';
import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';

export class DeepSeek<PERSON>rovider extends BaseLLMProvider {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.baseUrl = config.baseUrl || 'https://api.deepseek.com';
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  get providerId(): string {
    return 'deepseek';
  }

  get providerName(): string {
    return 'DeepSeek';
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      return false;
    }
  }

  async chat(
    messages: ChatMessage[],
    model: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      functions?: BuiltinFunction[];
    }
  ): Promise<LLMResponse> {
    const formattedMessages = this.formatMessagesForProvider(messages, options?.systemPrompt);
    
    const requestData: any = {
      model,
      messages: formattedMessages,
      temperature: options?.temperature ?? 0.7,
      stream: false
    };

    if (options?.maxTokens) {
      requestData.max_tokens = options.maxTokens;
    }

    // Add function calling if functions are provided
    if (options?.functions && options.functions.length > 0) {
      requestData.tools = options.functions.map(func => ({
        type: 'function',
        function: {
          name: func.name,
          description: func.description,
          parameters: func.parameters
        }
      }));
      requestData.tool_choice = 'auto';
    }

    try {
      const response = await this.client.post('/chat/completions', requestData);
      const choice = response.data.choices[0];
      
      if (!choice) {
        throw new Error('No response from DeepSeek');
      }

      let content = choice.message.content || '';
      const functionCalls: FunctionCall[] = [];

      // Handle function calls
      if (choice.message.tool_calls) {
        for (const toolCall of choice.message.tool_calls) {
          if (toolCall.type === 'function') {
            const functionCall: FunctionCall = {
              id: toolCall.id,
              name: toolCall.function.name,
              arguments: JSON.parse(toolCall.function.arguments)
            };

            try {
              functionCall.result = await this.executeFunctionCall(functionCall);
            } catch (error) {
              functionCall.error = error instanceof Error ? error.message : String(error);
            }

            functionCalls.push(functionCall);
          }
        }
      }

      return {
        content,
        functionCalls,
        usage: {
          inputTokens: response.data.usage?.prompt_tokens || 0,
          outputTokens: response.data.usage?.completion_tokens || 0,
          totalTokens: response.data.usage?.total_tokens || 0
        },
        model,
        provider: this.providerId
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`DeepSeek API error: ${error.response?.data?.error?.message || error.message}`);
      }
      throw new Error(`DeepSeek API error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
