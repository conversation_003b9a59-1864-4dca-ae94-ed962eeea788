/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import Conf from 'conf';
import { DEFAULT_SYSTEM_PROMPT } from './providers.js';
import { DEFAULT_THEME_ID } from './themes.js';
export class ConfigManager {
    config;
    constructor() {
        this.config = new Conf({
            projectName: 'arien-ai-cli',
            defaults: {
                theme: DEFAULT_THEME_ID,
                systemPrompt: DEFAULT_SYSTEM_PROMPT,
                authConfigs: {},
                chatHistory: [],
                preferences: {
                    autoSave: true,
                    showTokenCount: true,
                    enableFunctionCalling: true,
                    maxHistoryLength: 100
                }
            }
        });
    }
    get(key) {
        return this.config.get(key);
    }
    set(key, value) {
        this.config.set(key, value);
    }
    getAuthConfig(providerId) {
        const authConfigs = this.get('authConfigs');
        return authConfigs[providerId];
    }
    setAuthConfig(providerId, authConfig) {
        const authConfigs = this.get('authConfigs');
        authConfigs[providerId] = authConfig;
        this.set('authConfigs', authConfigs);
    }
    removeAuthConfig(providerId) {
        const authConfigs = this.get('authConfigs');
        delete authConfigs[providerId];
        this.set('authConfigs', authConfigs);
    }
    isProviderAuthenticated(providerId) {
        const authConfig = this.getAuthConfig(providerId);
        if (!authConfig)
            return false;
        // Check if API key exists
        if (authConfig.apiKey)
            return true;
        // Check if OAuth tokens exist and are not expired
        if (authConfig.accessToken) {
            if (!authConfig.expiresAt)
                return true;
            return Date.now() < authConfig.expiresAt;
        }
        return false;
    }
    getCurrentProvider() {
        return this.get('currentProvider');
    }
    getCurrentModel() {
        return this.get('currentModel');
    }
    setCurrentProvider(providerId, modelId) {
        this.set('currentProvider', providerId);
        if (modelId) {
            this.set('currentModel', modelId);
        }
    }
    getTheme() {
        return this.get('theme');
    }
    setTheme(themeId) {
        this.set('theme', themeId);
    }
    getSystemPrompt() {
        return this.get('systemPrompt');
    }
    setSystemPrompt(prompt) {
        this.set('systemPrompt', prompt);
    }
    addChatSession(session) {
        const history = this.get('chatHistory');
        history.unshift(session);
        // Limit history length
        const maxLength = this.get('preferences').maxHistoryLength;
        if (history.length > maxLength) {
            history.splice(maxLength);
        }
        this.set('chatHistory', history);
    }
    getChatHistory() {
        return this.get('chatHistory');
    }
    clearChatHistory() {
        this.set('chatHistory', []);
    }
    reset() {
        this.config.clear();
    }
    getConfigPath() {
        return this.config.path;
    }
}
export const configManager = new ConfigManager();
//# sourceMappingURL=index.js.map