{"version": 3, "file": "App.js", "sourceRoot": "", "sources": ["../../src/cli/App.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC1B,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAC;AAC3D,OAAO,EAAE,aAAa,EAAE,MAAM,+BAA+B,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAE,MAAM,kBAAkB,CAAC;AAG7C,MAAM,CAAC,MAAM,GAAG,GAAa,GAAG,EAAE;IAChC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAW;QAC3C,eAAe,EAAE,KAAK;QACtB,aAAa,EAAE,MAAM;QACrB,aAAa,EAAE,SAAS,CAAC,QAAQ,EAAE;QACnC,SAAS,EAAE,KAAK;KACjB,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,yCAAyC;QACzC,MAAM,eAAe,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;QACvD,IAAI,eAAe,IAAI,SAAS,CAAC,uBAAuB,CAAC,eAAe,CAAC,EAAE,CAAC;YAC1E,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChB,GAAG,IAAI;gBACP,eAAe,EAAE,IAAI;gBACrB,aAAa,EAAE,iBAAiB;gBAChC,gBAAgB,EAAE,eAAe;gBACjC,aAAa,EAAE,SAAS,CAAC,eAAe,EAAE;aAC3C,CAAC,CAAC,CAAC;QACN,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,oBAAoB,GAAG,CAAC,UAAkB,EAAE,OAAe,EAAE,EAAE;QACnE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,GAAG,IAAI;YACP,eAAe,EAAE,IAAI;YACrB,aAAa,EAAE,iBAAiB;YAChC,gBAAgB,EAAE,UAAU;YAC5B,aAAa,EAAE,OAAO;SACvB,CAAC,CAAC,CAAC;QAEJ,SAAS,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,CAAC,OAAe,EAAE,EAAE;QAC/C,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,GAAG,IAAI;YACP,aAAa,EAAE,OAAO;YACtB,aAAa,EAAE,MAAM;SACtB,CAAC,CAAC,CAAC;QAEJ,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,EAAE;QACpC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,GAAG,IAAI;YACP,KAAK;YACL,SAAS,EAAE,KAAK;SACjB,CAAC,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,OAAgB,EAAE,EAAE;QACtC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChB,GAAG,IAAI;YACP,SAAS,EAAE,OAAO;SACnB,CAAC,CAAC,CAAC;IACN,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,GAAG,EAAE;QAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,KAAC,aAAa,KAAG,CAAC;QAC3B,CAAC;QAED,QAAQ,KAAK,CAAC,aAAa,EAAE,CAAC;YAC5B,KAAK,MAAM;gBACT,OAAO,CACL,KAAC,UAAU,IACT,eAAe,EAAE,oBAAoB,EACrC,OAAO,EAAE,WAAW,EACpB,UAAU,EAAE,UAAU,GACtB,CACH,CAAC;YAEJ,KAAK,iBAAiB;gBACpB,OAAO,CACL,KAAC,oBAAoB,IACnB,YAAY,EAAE,KAAK,CAAC,aAAa,EACjC,eAAe,EAAE,oBAAoB,EACrC,OAAO,EAAE,WAAW,GACpB,CACH,CAAC;YAEJ,KAAK,MAAM;gBACT,OAAO,CACL,KAAC,UAAU,IACT,QAAQ,EAAE,KAAK,CAAC,gBAAiB,EACjC,KAAK,EAAE,KAAK,CAAC,aAAc,EAC3B,KAAK,EAAE,KAAK,CAAC,aAAa,EAC1B,OAAO,EAAE,WAAW,EACpB,UAAU,EAAE,UAAU,GACtB,CACH,CAAC;YAEJ;gBACE,OAAO,KAAC,UAAU,IAAC,eAAe,EAAE,oBAAoB,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,GAAI,CAAC;QAC/G,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,KAAC,aAAa,cACZ,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,SAAS,EAAE,EAAE,aACtC,mBAAmB,EAAE,EACrB,KAAK,CAAC,KAAK,IAAI,CACd,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YAC5B,MAAC,GAAG,IAAC,WAAW,EAAC,OAAO,EAAC,WAAW,EAAC,KAAK,EAAC,QAAQ,EAAE,CAAC,wBAC5C,KAAK,CAAC,KAAK,IACf,GACF,CACP,IACG,GACQ,CACjB,CAAC;AACJ,CAAC,CAAC"}