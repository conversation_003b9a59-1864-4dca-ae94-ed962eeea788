import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import SelectInput from 'ink-select-input';
import TextInput from 'ink-text-input';
import { Header } from '../components/Header.js';
import { PROVIDERS } from '../../core/config/providers.js';
import { arienCore } from '../../core/index.js';
export const AuthScreen = ({ onAuthenticated, onError, setLoading }) => {
    const [authState, setAuthState] = useState({
        step: 'provider',
        configValues: {},
        currentConfigIndex: 0
    });
    const [inputValue, setInputValue] = useState('');
    // Provider selection
    const providerItems = PROVIDERS.map(provider => ({
        label: `${provider.name} (${provider.models.length} models)`,
        value: provider.id
    }));
    const handleProviderSelect = (item) => {
        const provider = PROVIDERS.find(p => p.id === item.value);
        if (provider) {
            setAuthState(prev => ({
                ...prev,
                step: 'model',
                selectedProvider: provider
            }));
        }
    };
    // Model selection
    const modelItems = authState.selectedProvider?.models.map(model => ({
        label: `${model.name} - ${model.description}`,
        value: model.id
    })) || [];
    const handleModelSelect = (item) => {
        const model = authState.selectedProvider?.models.find(m => m.id === item.value);
        if (model) {
            setAuthState(prev => ({
                ...prev,
                step: 'config',
                selectedModel: model,
                currentConfigIndex: 0
            }));
        }
    };
    // Configuration input
    const currentConfigField = authState.selectedProvider?.configFields[authState.currentConfigIndex];
    const handleConfigInput = () => {
        if (!currentConfigField)
            return;
        const newConfigValues = {
            ...authState.configValues,
            [currentConfigField.key]: inputValue
        };
        const nextIndex = authState.currentConfigIndex + 1;
        const hasMoreFields = authState.selectedProvider && nextIndex < authState.selectedProvider.configFields.length;
        if (hasMoreFields) {
            setAuthState(prev => ({
                ...prev,
                configValues: newConfigValues,
                currentConfigIndex: nextIndex
            }));
            setInputValue('');
        }
        else {
            // All config fields completed, start authentication
            setAuthState(prev => ({
                ...prev,
                step: 'authenticating',
                configValues: newConfigValues
            }));
            authenticateProvider(newConfigValues);
        }
    };
    const authenticateProvider = async (configValues) => {
        if (!authState.selectedProvider || !authState.selectedModel)
            return;
        setLoading(true);
        try {
            const authConfig = {
                providerId: authState.selectedProvider.id,
                apiKey: configValues.apiKey,
                accessToken: configValues.accessToken,
                additionalConfig: configValues
            };
            const success = await arienCore.authenticateProvider(authState.selectedProvider.id, authConfig);
            if (success) {
                onAuthenticated(authState.selectedProvider.id, authState.selectedModel.id);
            }
            else {
                onError('Authentication failed. Please check your credentials.');
                setAuthState(prev => ({ ...prev, step: 'config', currentConfigIndex: 0 }));
            }
        }
        catch (error) {
            onError(`Authentication error: ${error instanceof Error ? error.message : String(error)}`);
            setAuthState(prev => ({ ...prev, step: 'config', currentConfigIndex: 0 }));
        }
        finally {
            setLoading(false);
        }
    };
    // Handle keyboard input for config step
    useInput((input, key) => {
        if (authState.step === 'config' && key.return) {
            handleConfigInput();
        }
    });
    const renderCurrentStep = () => {
        switch (authState.step) {
            case 'provider':
                return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "cyan", bold: true, children: "Select AI Provider:" }), _jsx(SelectInput, { items: providerItems, onSelect: handleProviderSelect }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "gray", dimColor: true, children: "Use \u2191\u2193 arrows to navigate, Enter to select" }) })] }));
            case 'model':
                return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "cyan", bold: true, children: ["Select Model for ", authState.selectedProvider?.name, ":"] }), _jsx(SelectInput, { items: modelItems, onSelect: handleModelSelect }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: "gray", dimColor: true, children: "Use \u2191\u2193 arrows to navigate, Enter to select" }) })] }));
            case 'config':
                if (!currentConfigField)
                    return null;
                return (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "cyan", bold: true, children: ["Configure ", authState.selectedProvider?.name, ":"] }), _jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: "yellow", children: [currentConfigField.label, currentConfigField.required && _jsx(Text, { color: "red", children: " *" })] }) }), currentConfigField.description && (_jsx(Box, { marginBottom: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: currentConfigField.description }) })), _jsxs(Box, { children: [_jsx(Text, { color: "white", children: currentConfigField.type === 'password' ? '🔒 ' : '📝 ' }), _jsx(TextInput, { value: inputValue, onChange: setInputValue, placeholder: `Enter ${currentConfigField.label.toLowerCase()}...`, mask: currentConfigField.type === 'password' ? '*' : undefined })] }), _jsx(Box, { marginTop: 2, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["Press Enter to continue (", authState.currentConfigIndex + 1, "/", authState.selectedProvider?.configFields.length, ")"] }) })] }));
            case 'authenticating':
                return (_jsxs(Box, { flexDirection: "column", alignItems: "center", children: [_jsxs(Text, { color: "cyan", bold: true, children: ["Authenticating with ", authState.selectedProvider?.name, "..."] }), _jsx(Text, { color: "gray", dimColor: true, children: "Please wait while we verify your credentials" })] }));
            default:
                return null;
        }
    };
    return (_jsxs(Box, { flexDirection: "column", padding: 2, children: [_jsx(Header, { title: "ARIEN AI", subtitle: "AI-Powered CLI Terminal", showLogo: true }), _jsx(Box, { marginTop: 2, children: renderCurrentStep() })] }));
};
//# sourceMappingURL=AuthScreen.js.map