"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthScreen = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
const react_1 = require("react");
const ink_1 = require("ink");
const ink_select_input_1 = __importDefault(require("ink-select-input"));
const ink_text_input_1 = __importDefault(require("ink-text-input"));
const Header_1 = require("../components/Header");
const providers_1 = require("../../core/config/providers");
const core_1 = require("../../core");
const AuthScreen = ({ onAuthenticated, onError, setLoading }) => {
    const [authState, setAuthState] = (0, react_1.useState)({
        step: 'provider',
        configValues: {},
        currentConfigIndex: 0
    });
    const [inputValue, setInputValue] = (0, react_1.useState)('');
    // Provider selection
    const providerItems = providers_1.PROVIDERS.map(provider => ({
        label: `${provider.name} (${provider.models.length} models)`,
        value: provider.id
    }));
    const handleProviderSelect = (item) => {
        const provider = providers_1.PROVIDERS.find(p => p.id === item.value);
        if (provider) {
            setAuthState(prev => ({
                ...prev,
                step: 'model',
                selectedProvider: provider
            }));
        }
    };
    // Model selection
    const modelItems = authState.selectedProvider?.models.map(model => ({
        label: `${model.name} - ${model.description}`,
        value: model.id
    })) || [];
    const handleModelSelect = (item) => {
        const model = authState.selectedProvider?.models.find(m => m.id === item.value);
        if (model) {
            setAuthState(prev => ({
                ...prev,
                step: 'config',
                selectedModel: model,
                currentConfigIndex: 0
            }));
        }
    };
    // Configuration input
    const currentConfigField = authState.selectedProvider?.configFields[authState.currentConfigIndex];
    const handleConfigInput = () => {
        if (!currentConfigField)
            return;
        const newConfigValues = {
            ...authState.configValues,
            [currentConfigField.key]: inputValue
        };
        const nextIndex = authState.currentConfigIndex + 1;
        const hasMoreFields = authState.selectedProvider && nextIndex < authState.selectedProvider.configFields.length;
        if (hasMoreFields) {
            setAuthState(prev => ({
                ...prev,
                configValues: newConfigValues,
                currentConfigIndex: nextIndex
            }));
            setInputValue('');
        }
        else {
            // All config fields completed, start authentication
            setAuthState(prev => ({
                ...prev,
                step: 'authenticating',
                configValues: newConfigValues
            }));
            authenticateProvider(newConfigValues);
        }
    };
    const authenticateProvider = async (configValues) => {
        if (!authState.selectedProvider || !authState.selectedModel)
            return;
        setLoading(true);
        try {
            const authConfig = {
                providerId: authState.selectedProvider.id,
                apiKey: configValues.apiKey,
                accessToken: configValues.accessToken,
                additionalConfig: configValues
            };
            const success = await core_1.arienCore.authenticateProvider(authState.selectedProvider.id, authConfig);
            if (success) {
                onAuthenticated(authState.selectedProvider.id, authState.selectedModel.id);
            }
            else {
                onError('Authentication failed. Please check your credentials.');
                setAuthState(prev => ({ ...prev, step: 'config', currentConfigIndex: 0 }));
            }
        }
        catch (error) {
            onError(`Authentication error: ${error instanceof Error ? error.message : String(error)}`);
            setAuthState(prev => ({ ...prev, step: 'config', currentConfigIndex: 0 }));
        }
        finally {
            setLoading(false);
        }
    };
    // Handle keyboard input for config step
    (0, ink_1.useInput)((input, key) => {
        if (authState.step === 'config' && key.return) {
            handleConfigInput();
        }
    });
    const renderCurrentStep = () => {
        switch (authState.step) {
            case 'provider':
                return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "Select AI Provider:" }), (0, jsx_runtime_1.jsx)(ink_select_input_1.default, { items: providerItems, onSelect: handleProviderSelect }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: "Use \u2191\u2193 arrows to navigate, Enter to select" }) })] }));
            case 'model':
                return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "cyan", bold: true, children: ["Select Model for ", authState.selectedProvider?.name, ":"] }), (0, jsx_runtime_1.jsx)(ink_select_input_1.default, { items: modelItems, onSelect: handleModelSelect }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: "Use \u2191\u2193 arrows to navigate, Enter to select" }) })] }));
            case 'config':
                if (!currentConfigField)
                    return null;
                return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "cyan", bold: true, children: ["Configure ", authState.selectedProvider?.name, ":"] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "yellow", children: [currentConfigField.label, currentConfigField.required && (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", children: " *" })] }) }), currentConfigField.description && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: currentConfigField.description }) })), (0, jsx_runtime_1.jsxs)(ink_1.Box, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "white", children: currentConfigField.type === 'password' ? '🔒 ' : '📝 ' }), (0, jsx_runtime_1.jsx)(ink_text_input_1.default, { value: inputValue, onChange: setInputValue, placeholder: `Enter ${currentConfigField.label.toLowerCase()}...`, mask: currentConfigField.type === 'password' ? '*' : undefined })] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", dimColor: true, children: ["Press Enter to continue (", authState.currentConfigIndex + 1, "/", authState.selectedProvider?.configFields.length, ")"] }) })] }));
            case 'authenticating':
                return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", alignItems: "center", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "cyan", bold: true, children: ["Authenticating with ", authState.selectedProvider?.name, "..."] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: "Please wait while we verify your credentials" })] }));
            default:
                return null;
        }
    };
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", padding: 2, children: [(0, jsx_runtime_1.jsx)(Header_1.Header, { title: "ARIEN AI", subtitle: "AI-Powered CLI Terminal", showLogo: true }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: renderCurrentStep() })] }));
};
exports.AuthScreen = AuthScreen;
//# sourceMappingURL=AuthScreen.js.map