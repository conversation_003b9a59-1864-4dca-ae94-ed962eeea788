/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
export * from './types/index.js';
export * from './config/index.js';
export * from './providers/index.js';
export * from './functions/index.js';
import { ChatMessage, LLMResponse, AuthConfig } from './types/index.js';
export declare class ArienCore {
    constructor();
    private initializeProviders;
    authenticateProvider(providerId: string, authConfig: AuthConfig): Promise<boolean>;
    chat(messages: ChatMessage[], providerId?: string, modelId?: string, options?: {
        temperature?: number;
        maxTokens?: number;
        systemPrompt?: string;
        enableFunctions?: boolean;
    }): Promise<LLMResponse>;
    isProviderAuthenticated(providerId: string): boolean;
    getCurrentProvider(): string | undefined;
    getCurrentModel(): string | undefined;
    setCurrentProvider(providerId: string, modelId?: string): void;
    getTheme(): string;
    setTheme(themeId: string): void;
    getSystemPrompt(): string;
    setSystemPrompt(prompt: string): void;
    addChatSession(session: any): void;
    getChatHistory(): any[];
    clearChatHistory(): void;
    reset(): void;
}
export declare const arienCore: ArienCore;
//# sourceMappingURL=index.d.ts.map