/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Theme } from '../../core/types/index.js';

interface ThemePreviewProps {
  theme: Theme;
  isSelected?: boolean;
  isActive?: boolean;
}

export const ThemePreview: React.FC<ThemePreviewProps> = ({ 
  theme, 
  isSelected = false, 
  isActive = false 
}) => {
  const borderColor = isSelected ? theme.colors.accent : theme.colors.muted;
  const borderStyle = isActive ? 'double' : 'single';

  return (
    <Box 
      borderStyle={borderStyle} 
      borderColor={borderColor}
      padding={1}
      width={30}
      flexDirection="column"
    >
      {/* Theme name */}
      <Box marginBottom={1}>
        <Text bold color={theme.colors.primary}>{theme.name}</Text>
      </Box>

      {/* Theme description */}
      <Box marginBottom={1}>
        <Text color={theme.colors.text} dimColor>
          {theme.description}
        </Text>
      </Box>

      {/* Color palette preview */}
      <Box flexDirection="column">
        <Box>
          <Text color={theme.colors.primary}>● Primary</Text>
          <Text> </Text>
          <Text color={theme.colors.secondary}>● Secondary</Text>
        </Box>
        <Box>
          <Text color={theme.colors.accent}>● Accent</Text>
          <Text> </Text>
          <Text color={theme.colors.success}>● Success</Text>
        </Box>
        <Box>
          <Text color={theme.colors.warning}>● Warning</Text>
          <Text> </Text>
          <Text color={theme.colors.error}>● Error</Text>
        </Box>
      </Box>

      {/* Sample chat preview */}
      <Box marginTop={1} flexDirection="column">
        <Text color={theme.colors.muted} dimColor>
          Chat Preview:
        </Text>
        <Box 
          borderStyle="round" 
          borderColor={theme.colors.primary}
          paddingX={1}
          marginTop={1}
        >
          <Box flexDirection="column">
            <Text color={theme.colors.text}>
              <Text color={theme.colors.accent}>You:</Text> Hello!
            </Text>
            <Text color={theme.colors.text}>
              <Text color={theme.colors.primary}>AI:</Text> Hi there! 👋
            </Text>
          </Box>
        </Box>
      </Box>

      {isSelected && (
        <Box marginTop={1} justifyContent="center">
          <Text color={theme.colors.accent} bold>
            ▶ SELECTED
          </Text>
        </Box>
      )}
    </Box>
  );
};
