/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';

export interface LLMProviderConfig {
  apiKey?: string;
  baseUrl?: string;
  organization?: string;
  projectId?: string;
  accessToken?: string;
  [key: string]: any;
}

export abstract class BaseLLMProvider {
  protected config: LLMProviderConfig;
  protected builtinFunctions: BuiltinFunction[] = [];

  constructor(config: LLMProviderConfig) {
    this.config = config;
  }

  abstract get providerId(): string;
  abstract get providerName(): string;

  abstract chat(
    messages: ChatMessage[],
    model: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      functions?: BuiltinFunction[];
    }
  ): Promise<LLMResponse>;

  abstract validateConfig(): Promise<boolean>;

  setBuiltinFunctions(functions: BuiltinFunction[]): void {
    this.builtinFunctions = functions;
  }

  protected async executeFunctionCall(functionCall: FunctionCall): Promise<any> {
    const func = this.builtinFunctions.find(f => f.name === functionCall.name);
    if (!func) {
      throw new Error(`Unknown function: ${functionCall.name}`);
    }

    try {
      return await func.handler(functionCall.arguments);
    } catch (error) {
      throw new Error(`Function execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  protected formatMessagesForProvider(messages: ChatMessage[], systemPrompt?: string): any[] {
    const formattedMessages: any[] = [];

    // Add system message if provided
    if (systemPrompt) {
      formattedMessages.push({
        role: 'system',
        content: systemPrompt
      });
    }

    // Add conversation messages
    for (const message of messages) {
      formattedMessages.push({
        role: message.role,
        content: message.content
      });
    }

    return formattedMessages;
  }

  protected formatFunctionsForProvider(functions: BuiltinFunction[]): any[] {
    return functions.map(func => ({
      name: func.name,
      description: func.description,
      parameters: func.parameters
    }));
  }

  protected createChatMessage(
    role: 'user' | 'assistant' | 'system',
    content: string,
    metadata?: any
  ): ChatMessage {
    return {
      id: this.generateId(),
      role,
      content,
      timestamp: new Date(),
      metadata
    };
  }

  protected generateId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  protected calculateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }
}
