/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import SelectInput from 'ink-select-input';
import TextInput from 'ink-text-input';
import { Header } from '../components/Header.js';
import { PROVIDERS } from '../../core/config/providers.js';
import { arienCore } from '../../core/index.js';
import { LLMProvider, LLMModel, ConfigField } from '../../core/types/index.js';

interface AuthScreenProps {
  onAuthenticated: (providerId: string, modelId: string) => void;
  onError: (error: string) => void;
  setLoading: (loading: boolean) => void;
}

interface AuthStep {
  step: 'provider' | 'model' | 'config' | 'authenticating';
  selectedProvider?: LLMProvider;
  selectedModel?: LLMModel;
  configValues: Record<string, string>;
  currentConfigIndex: number;
}

export const AuthScreen: React.FC<AuthScreenProps> = ({ 
  onAuthenticated, 
  onError, 
  setLoading 
}) => {
  const [authState, setAuthState] = useState<AuthStep>({
    step: 'provider',
    configValues: {},
    currentConfigIndex: 0
  });

  const [inputValue, setInputValue] = useState('');

  // Provider selection
  const providerItems = PROVIDERS.map(provider => ({
    label: `${provider.name} (${provider.models.length} models)`,
    value: provider.id
  }));

  const handleProviderSelect = (item: { value: string }) => {
    const provider = PROVIDERS.find(p => p.id === item.value);
    if (provider) {
      setAuthState(prev => ({
        ...prev,
        step: 'model',
        selectedProvider: provider
      }));
    }
  };

  // Model selection
  const modelItems = authState.selectedProvider?.models.map(model => ({
    label: `${model.name} - ${model.description}`,
    value: model.id
  })) || [];

  const handleModelSelect = (item: { value: string }) => {
    const model = authState.selectedProvider?.models.find(m => m.id === item.value);
    if (model) {
      setAuthState(prev => ({
        ...prev,
        step: 'config',
        selectedModel: model,
        currentConfigIndex: 0
      }));
    }
  };

  // Configuration input
  const currentConfigField = authState.selectedProvider?.configFields[authState.currentConfigIndex];

  const handleConfigInput = () => {
    if (!currentConfigField) return;

    const newConfigValues = {
      ...authState.configValues,
      [currentConfigField.key]: inputValue
    };

    const nextIndex = authState.currentConfigIndex + 1;
    const hasMoreFields = authState.selectedProvider && nextIndex < authState.selectedProvider.configFields.length;

    if (hasMoreFields) {
      setAuthState(prev => ({
        ...prev,
        configValues: newConfigValues,
        currentConfigIndex: nextIndex
      }));
      setInputValue('');
    } else {
      // All config fields completed, start authentication
      setAuthState(prev => ({
        ...prev,
        step: 'authenticating',
        configValues: newConfigValues
      }));
      authenticateProvider(newConfigValues);
    }
  };

  const authenticateProvider = async (configValues: Record<string, string>) => {
    if (!authState.selectedProvider || !authState.selectedModel) return;

    setLoading(true);

    try {
      const authConfig = {
        providerId: authState.selectedProvider.id,
        apiKey: configValues.apiKey,
        accessToken: configValues.accessToken,
        additionalConfig: configValues
      };

      const success = await arienCore.authenticateProvider(
        authState.selectedProvider.id,
        authConfig
      );

      if (success) {
        onAuthenticated(authState.selectedProvider.id, authState.selectedModel.id);
      } else {
        onError('Authentication failed. Please check your credentials.');
        setAuthState(prev => ({ ...prev, step: 'config', currentConfigIndex: 0 }));
      }
    } catch (error) {
      onError(`Authentication error: ${error instanceof Error ? error.message : String(error)}`);
      setAuthState(prev => ({ ...prev, step: 'config', currentConfigIndex: 0 }));
    } finally {
      setLoading(false);
    }
  };

  // Handle keyboard input for config step
  useInput((input, key) => {
    if (authState.step === 'config' && key.return) {
      handleConfigInput();
    }
  });

  const renderCurrentStep = () => {
    switch (authState.step) {
      case 'provider':
        return (
          <Box flexDirection="column">
            <Text color="cyan" bold>
              Select AI Provider:
            </Text>
            <SelectInput items={providerItems} onSelect={handleProviderSelect} />
            <Box marginTop={2}>
              <Text color="gray" dimColor>
                Use ↑↓ arrows to navigate, Enter to select
              </Text>
            </Box>
          </Box>
        );

      case 'model':
        return (
          <Box flexDirection="column">
            <Text color="cyan" bold>
              Select Model for {authState.selectedProvider?.name}:
            </Text>
            <SelectInput items={modelItems} onSelect={handleModelSelect} />
            <Box marginTop={2}>
              <Text color="gray" dimColor>
                Use ↑↓ arrows to navigate, Enter to select
              </Text>
            </Box>
          </Box>
        );

      case 'config':
        if (!currentConfigField) return null;
        
        return (
          <Box flexDirection="column">
            <Text color="cyan" bold>
              Configure {authState.selectedProvider?.name}:
            </Text>
            
            <Box marginBottom={1}>
              <Text color="yellow">
                {currentConfigField.label}
                {currentConfigField.required && <Text color="red"> *</Text>}
              </Text>
            </Box>
            
            {currentConfigField.description && (
              <Box marginBottom={1}>
                <Text color="gray" dimColor>
                  {currentConfigField.description}
                </Text>
              </Box>
            )}
            
            <Box>
              <Text color="white">
                {currentConfigField.type === 'password' ? '🔒 ' : '📝 '}
              </Text>
              <TextInput
                value={inputValue}
                onChange={setInputValue}
                placeholder={`Enter ${currentConfigField.label.toLowerCase()}...`}
                mask={currentConfigField.type === 'password' ? '*' : undefined}
              />
            </Box>
            
            <Box marginTop={2}>
              <Text color="gray" dimColor>
                Press Enter to continue ({authState.currentConfigIndex + 1}/{authState.selectedProvider?.configFields.length})
              </Text>
            </Box>
          </Box>
        );

      case 'authenticating':
        return (
          <Box flexDirection="column" alignItems="center">
            <Text color="cyan" bold>
              Authenticating with {authState.selectedProvider?.name}...
            </Text>
            <Text color="gray" dimColor>
              Please wait while we verify your credentials
            </Text>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Box flexDirection="column" padding={2}>
      <Header 
        title="ARIEN AI"
        subtitle="AI-Powered CLI Terminal"
        showLogo={true}
      />
      
      <Box marginTop={2}>
        {renderCurrentStep()}
      </Box>
    </Box>
  );
};
