/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';
export interface LLMProviderConfig {
    apiKey?: string;
    baseUrl?: string;
    organization?: string;
    projectId?: string;
    accessToken?: string;
    [key: string]: any;
}
export declare abstract class BaseLLMProvider {
    protected config: LLMProviderConfig;
    protected builtinFunctions: BuiltinFunction[];
    constructor(config: LLMProviderConfig);
    abstract get providerId(): string;
    abstract get providerName(): string;
    abstract chat(messages: ChatMessage[], model: string, options?: {
        temperature?: number;
        maxTokens?: number;
        systemPrompt?: string;
        functions?: BuiltinFunction[];
    }): Promise<LLMResponse>;
    abstract validateConfig(): Promise<boolean>;
    setBuiltinFunctions(functions: BuiltinFunction[]): void;
    protected executeFunctionCall(functionCall: FunctionCall): Promise<any>;
    protected formatMessagesForProvider(messages: ChatMessage[], systemPrompt?: string): any[];
    protected formatFunctionsForProvider(functions: BuiltinFunction[]): any[];
    protected createChatMessage(role: 'user' | 'assistant' | 'system', content: string, metadata?: any): ChatMessage;
    protected generateId(): string;
    protected calculateTokens(text: string): number;
}
//# sourceMappingURL=base.d.ts.map