/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { AppConfig, AuthConfig } from '../types/index.js';
export declare class ConfigManager {
    private config;
    constructor();
    get<K extends keyof AppConfig>(key: K): AppConfig[K];
    set<K extends keyof AppConfig>(key: K, value: AppConfig[K]): void;
    getAuthConfig(providerId: string): AuthConfig | undefined;
    setAuthConfig(providerId: string, authConfig: AuthConfig): void;
    removeAuthConfig(providerId: string): void;
    isProviderAuthenticated(providerId: string): boolean;
    getCurrentProvider(): string | undefined;
    getCurrentModel(): string | undefined;
    setCurrentProvider(providerId: string, modelId?: string): void;
    getTheme(): string;
    setTheme(themeId: string): void;
    getSystemPrompt(): string;
    setSystemPrompt(prompt: string): void;
    addChatSession(session: any): void;
    getChatHistory(): any[];
    clearChatHistory(): void;
    reset(): void;
    getConfigPath(): string;
}
export declare const configManager: ConfigManager;
//# sourceMappingURL=index.d.ts.map