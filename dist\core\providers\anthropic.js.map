{"version": 3, "file": "anthropic.js", "sourceRoot": "", "sources": ["../../../src/core/providers/anthropic.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAC1C,OAAO,EAAE,eAAe,EAAqB,MAAM,WAAW,CAAC;AAG/D,MAAM,OAAO,iBAAkB,SAAQ,eAAe;IAC5C,MAAM,CAAY;IAE1B,YAAY,MAAyB;QACnC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC;YAC1B,MAAM,EAAE,MAAM,CAAC,MAAM;SACtB,CAAC,CAAC;IACL,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,IAAI,YAAY;QACd,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE,2BAA2B;gBAClC,UAAU,EAAE,CAAC;gBACb,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;aAC5C,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,KAAa,EACb,OAKC;QAED,sDAAsD;QACtD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QACvE,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACzD,IAAI,EAAE,GAAG,CAAC,IAA4B;YACtC,OAAO,EAAE,GAAG,CAAC,OAAO;SACrB,CAAC,CAAC,CAAC;QAEJ,MAAM,aAAa,GAAkC;YACnD,KAAK;YACL,UAAU,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;YACtC,QAAQ,EAAE,iBAAiB;YAC3B,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;SACzC,CAAC;QAEF,gCAAgC;QAChC,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;YAC1B,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;QAC9C,CAAC;QAED,iDAAiD;QACjD,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvD,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnD,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,YAAY,EAAE,IAAI,CAAC,UAAU;aAC9B,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAElE,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,MAAM,aAAa,GAAmB,EAAE,CAAC;YAEzC,2BAA2B;YAC3B,KAAK,MAAM,YAAY,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC5C,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBACjC,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC;gBAC/B,CAAC;qBAAM,IAAI,YAAY,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAC5C,MAAM,YAAY,GAAiB;wBACjC,EAAE,EAAE,YAAY,CAAC,EAAE;wBACnB,IAAI,EAAE,YAAY,CAAC,IAAI;wBACvB,SAAS,EAAE,YAAY,CAAC,KAA4B;qBACrD,CAAC;oBAEF,IAAI,CAAC;wBACH,YAAY,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;oBACrE,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,YAAY,CAAC,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC9E,CAAC;oBAED,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,KAAK,EAAE;oBACL,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;oBACxC,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;oBAC1C,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa;iBACxE;gBACD,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,UAAU;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;CACF"}