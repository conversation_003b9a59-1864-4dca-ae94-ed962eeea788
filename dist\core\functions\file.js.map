{"version": 3, "file": "file.js", "sourceRoot": "", "sources": ["../../../src/core/functions/file.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,2BAAoC;AACpC,+BAAwD;AAG3C,QAAA,aAAa,GAAsB;IAC9C;QACE,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,6BAA6B;QAC1C,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,0BAA0B;iBACxC;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+BAA+B;oBAC5C,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;iBACzC;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2CAA2C;iBACzD;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,CAAC;SACnB;QACD,OAAO,EAAE,KAAK,EAAE,IAA2D,EAAE,EAAE;YAC7E,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,cAAc;gBAC3D,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvC,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,EAAE,CAAC;oBACzB,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,KAAK,EAAE,mBAAmB,KAAK,CAAC,IAAI,gBAAgB,OAAO,SAAS;wBACpE,IAAI,EAAE,IAAI,CAAC,IAAI;qBAChB,CAAC;gBACJ,CAAC;gBAED,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAe,IAAI,MAAM,CAAC,CAAC;gBAE7E,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,OAAO;oBACP,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,MAAM;iBAClC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IACD;QACE,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,yBAAyB;QACtC,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2BAA2B;iBACzC;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,8BAA8B;iBAC5C;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+BAA+B;oBAC5C,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;iBACzC;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,iEAAiE;iBAC/E;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;SAC9B;QACD,OAAO,EAAE,KAAK,EAAE,IAAgF,EAAE,EAAE;YAClG,IAAI,CAAC;gBACH,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBACpB,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC/B,MAAM,aAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC3C,CAAC;gBAED,MAAM,aAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAe,IAAI,MAAM,CAAC,CAAC;gBAE5E,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,MAAM;iBAClC,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IACD;QACE,IAAI,EAAE,gBAAgB;QACtB,WAAW,EAAE,4CAA4C;QACzD,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+BAA+B;iBAC7C;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,yCAAyC;iBACvD;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,uCAAuC;iBACrD;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,sCAAsC;iBACpD;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,CAAC;SACnB;QACD,OAAO,EAAE,KAAK,EAAE,IAAoF,EAAE,EAAE;YACtG,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;gBAEpC,MAAM,OAAO,GAAG,KAAK,EAAE,OAAe,EAAE,QAAgB,CAAC,EAAkB,EAAE;oBAC3E,IAAI,KAAK,GAAG,QAAQ;wBAAE,OAAO,EAAE,CAAC;oBAEhC,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACxC,MAAM,MAAM,GAAG,EAAE,CAAC;oBAElB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;4BAAE,SAAS;wBAEvD,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;wBACrC,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBAEtC,MAAM,QAAQ,GAAQ;4BACpB,IAAI,EAAE,IAAI;4BACV,IAAI,EAAE,QAAQ;4BACd,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;4BAChD,IAAI,EAAE,KAAK,CAAC,IAAI;4BAChB,QAAQ,EAAE,KAAK,CAAC,KAAK;4BACrB,WAAW,EAAE,KAAK,CAAC,IAAI;yBACxB,CAAC;wBAEF,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,GAAG,QAAQ,EAAE,CAAC;4BAC9D,QAAQ,CAAC,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;wBACzD,CAAC;wBAED,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACxB,CAAC;oBAED,OAAO,MAAM,CAAC;gBAChB,CAAC,CAAC;gBAEF,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,KAAK;oBACL,KAAK,EAAE,KAAK,CAAC,MAAM;iBACpB,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IACD;QACE,IAAI,EAAE,WAAW;QACjB,WAAW,EAAE,2CAA2C;QACxD,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+BAA+B;iBAC7C;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,CAAC;SACnB;QACD,OAAO,EAAE,KAAK,EAAE,IAAsB,EAAE,EAAE;YACxC,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,CAAC;oBACzB,SAAS,EAAE,IAAA,cAAO,EAAC,IAAI,CAAC,IAAI,CAAC;oBAC7B,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;oBAChD,IAAI,EAAE,KAAK,CAAC,IAAI;oBAChB,OAAO,EAAE,KAAK,CAAC,SAAS;oBACxB,QAAQ,EAAE,KAAK,CAAC,KAAK;oBACrB,QAAQ,EAAE,KAAK,CAAC,KAAK;oBACrB,WAAW,EAAE,KAAK,CAAC,IAAI;oBACvB,UAAU,EAAE,IAAI,EAAE,6CAA6C;oBAC/D,UAAU,EAAE,IAAI,CAAE,6CAA6C;iBAChE,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;KACF;IACD;QACE,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,4BAA4B;QACzC,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,yCAAyC;iBACvD;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,iDAAiD;iBAC/D;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,CAAC;SACnB;QACD,OAAO,EAAE,KAAK,EAAE,IAA2C,EAAE,EAAE;YAC7D,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEvC,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;oBACxB,MAAM,aAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;gBAC3D,CAAC;qBAAM,CAAC;oBACN,MAAM,aAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7B,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;iBACjD,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,CAAC,OAAO;oBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC;YACJ,CAAC;QACH,CAAC;KACF;CACF,CAAC"}