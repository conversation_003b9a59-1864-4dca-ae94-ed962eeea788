/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import OpenAI from 'openai';
import { BaseLLMProvider } from './base.js';
export class OpenAIProvider extends BaseLL<PERSON>rovider {
    client;
    constructor(config) {
        super(config);
        this.client = new OpenAI({
            apiKey: config.apiKey,
            organization: config.organization
        });
    }
    get providerId() {
        return 'openai';
    }
    get providerName() {
        return 'OpenAI';
    }
    async validateConfig() {
        try {
            await this.client.models.list();
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async chat(messages, model, options) {
        const formattedMessages = this.formatMessagesForProvider(messages, options?.systemPrompt);
        const requestParams = {
            model,
            messages: formattedMessages,
            temperature: options?.temperature ?? 0.7,
            max_tokens: options?.maxTokens
        };
        // Add function calling if functions are provided
        if (options?.functions && options.functions.length > 0) {
            requestParams.tools = options.functions.map(func => ({
                type: 'function',
                function: {
                    name: func.name,
                    description: func.description,
                    parameters: func.parameters
                }
            }));
            requestParams.tool_choice = 'auto';
        }
        try {
            const response = await this.client.chat.completions.create(requestParams);
            const choice = response.choices[0];
            if (!choice) {
                throw new Error('No response from OpenAI');
            }
            let content = choice.message.content || '';
            const functionCalls = [];
            // Handle function calls
            if (choice.message.tool_calls) {
                for (const toolCall of choice.message.tool_calls) {
                    if (toolCall.type === 'function') {
                        const functionCall = {
                            id: toolCall.id,
                            name: toolCall.function.name,
                            arguments: JSON.parse(toolCall.function.arguments)
                        };
                        try {
                            functionCall.result = await this.executeFunctionCall(functionCall);
                        }
                        catch (error) {
                            functionCall.error = error instanceof Error ? error.message : String(error);
                        }
                        functionCalls.push(functionCall);
                    }
                }
            }
            return {
                content,
                functionCalls,
                usage: {
                    inputTokens: response.usage?.prompt_tokens || 0,
                    outputTokens: response.usage?.completion_tokens || 0,
                    totalTokens: response.usage?.total_tokens || 0
                },
                model,
                provider: this.providerId
            };
        }
        catch (error) {
            throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
//# sourceMappingURL=openai.js.map