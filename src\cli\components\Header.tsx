/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Theme } from '../../core/types';

interface HeaderProps {
  title?: string;
  subtitle?: string;
  theme?: Theme;
  showLogo?: boolean;
}

export const Header: React.FC<HeaderProps> = ({ 
  title = 'ARIEN AI', 
  subtitle, 
  theme,
  showLogo = true 
}) => {
  const primaryColor = theme?.colors.primary || '#00ff9f';
  const textColor = theme?.colors.text || '#ffffff';

  return (
    <Box flexDirection="column" alignItems="center" marginBottom={1}>
      {showLogo && (
        <Box marginBottom={1}>
          <Text bold color={primaryColor}>
            {title}
          </Text>
        </Box>
      )}
      
      {subtitle && (
        <Box>
          <Text color={textColor} bold>
            {subtitle}
          </Text>
        </Box>
      )}
      
      <Box marginTop={1} width={60}>
        <Text color={theme?.colors.muted || '#666666'}>
          {'─'.repeat(60)}
        </Text>
      </Box>
    </Box>
  );
};
