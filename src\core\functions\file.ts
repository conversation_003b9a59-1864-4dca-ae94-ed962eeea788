/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { promises as fs } from 'fs';
import { join, dirname, basename, extname } from 'path';
import { BuiltinFunction } from '../types';

export const fileFunctions: BuiltinFunction[] = [
  {
    name: 'read_file',
    description: 'Read the contents of a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to read'
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf8)',
          enum: ['utf8', 'ascii', 'base64', 'hex']
        },
        maxSize: {
          type: 'number',
          description: 'Maximum file size in bytes (default: 1MB)'
        }
      },
      required: ['path']
    },
    handler: async (args: { path: string; encoding?: string; maxSize?: number }) => {
      try {
        const maxSize = args.maxSize || 1024 * 1024; // 1MB default
        const stats = await fs.stat(args.path);
        
        if (stats.size > maxSize) {
          return {
            success: false,
            error: `File too large: ${stats.size} bytes (max: ${maxSize} bytes)`,
            path: args.path
          };
        }

        const content = await fs.readFile(args.path, args.encoding as any || 'utf8');
        
        return {
          success: true,
          content,
          path: args.path,
          size: stats.size,
          encoding: args.encoding || 'utf8'
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message,
          path: args.path
        };
      }
    }
  },
  {
    name: 'write_file',
    description: 'Write content to a file',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file to write'
        },
        content: {
          type: 'string',
          description: 'Content to write to the file'
        },
        encoding: {
          type: 'string',
          description: 'File encoding (default: utf8)',
          enum: ['utf8', 'ascii', 'base64', 'hex']
        },
        createDirs: {
          type: 'boolean',
          description: 'Create parent directories if they don\'t exist (default: false)'
        }
      },
      required: ['path', 'content']
    },
    handler: async (args: { path: string; content: string; encoding?: string; createDirs?: boolean }) => {
      try {
        if (args.createDirs) {
          const dir = dirname(args.path);
          await fs.mkdir(dir, { recursive: true });
        }

        await fs.writeFile(args.path, args.content, args.encoding as any || 'utf8');
        
        const stats = await fs.stat(args.path);
        
        return {
          success: true,
          path: args.path,
          size: stats.size,
          encoding: args.encoding || 'utf8'
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message,
          path: args.path
        };
      }
    }
  },
  {
    name: 'list_directory',
    description: 'List files and directories in a given path',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the directory to list'
        },
        recursive: {
          type: 'boolean',
          description: 'List files recursively (default: false)'
        },
        showHidden: {
          type: 'boolean',
          description: 'Include hidden files (default: false)'
        },
        maxDepth: {
          type: 'number',
          description: 'Maximum recursion depth (default: 3)'
        }
      },
      required: ['path']
    },
    handler: async (args: { path: string; recursive?: boolean; showHidden?: boolean; maxDepth?: number }) => {
      try {
        const maxDepth = args.maxDepth || 3;
        
        const listDir = async (dirPath: string, depth: number = 0): Promise<any[]> => {
          if (depth > maxDepth) return [];
          
          const items = await fs.readdir(dirPath);
          const result = [];
          
          for (const item of items) {
            if (!args.showHidden && item.startsWith('.')) continue;
            
            const itemPath = join(dirPath, item);
            const stats = await fs.stat(itemPath);
            
            const itemInfo: any = {
              name: item,
              path: itemPath,
              type: stats.isDirectory() ? 'directory' : 'file',
              size: stats.size,
              modified: stats.mtime,
              permissions: stats.mode
            };

            if (args.recursive && stats.isDirectory() && depth < maxDepth) {
              itemInfo.children = await listDir(itemPath, depth + 1);
            }
            
            result.push(itemInfo);
          }
          
          return result;
        };

        const items = await listDir(args.path);
        
        return {
          success: true,
          path: args.path,
          items,
          count: items.length
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message,
          path: args.path
        };
      }
    }
  },
  {
    name: 'file_info',
    description: 'Get information about a file or directory',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file or directory'
        }
      },
      required: ['path']
    },
    handler: async (args: { path: string }) => {
      try {
        const stats = await fs.stat(args.path);
        
        return {
          success: true,
          path: args.path,
          name: basename(args.path),
          extension: extname(args.path),
          type: stats.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          accessed: stats.atime,
          permissions: stats.mode,
          isReadable: true, // Simplified - would need more complex check
          isWritable: true  // Simplified - would need more complex check
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message,
          path: args.path
        };
      }
    }
  },
  {
    name: 'delete_file',
    description: 'Delete a file or directory',
    parameters: {
      type: 'object',
      properties: {
        path: {
          type: 'string',
          description: 'Path to the file or directory to delete'
        },
        recursive: {
          type: 'boolean',
          description: 'Delete directories recursively (default: false)'
        }
      },
      required: ['path']
    },
    handler: async (args: { path: string; recursive?: boolean }) => {
      try {
        const stats = await fs.stat(args.path);
        
        if (stats.isDirectory()) {
          await fs.rmdir(args.path, { recursive: args.recursive });
        } else {
          await fs.unlink(args.path);
        }
        
        return {
          success: true,
          path: args.path,
          type: stats.isDirectory() ? 'directory' : 'file'
        };
      } catch (error: any) {
        return {
          success: false,
          error: error.message,
          path: args.path
        };
      }
    }
  }
];
