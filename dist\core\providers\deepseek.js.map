{"version": 3, "file": "deepseek.js", "sourceRoot": "", "sources": ["../../../src/core/providers/deepseek.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAwB,MAAM,OAAO,CAAC;AAC7C,OAAO,EAAE,eAAe,EAAqB,MAAM,WAAW,CAAC;AAG/D,MAAM,OAAO,gBAAiB,SAAQ,eAAe;IAC3C,MAAM,CAAgB;IACtB,OAAO,CAAS;IAExB,YAAY,MAAyB;QACnC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,0BAA0B,CAAC;QAC5D,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,MAAM,CAAC,MAAM,EAAE;gBAC1C,cAAc,EAAE,kBAAkB;aACnC;SACF,CAAC,CAAC;IACL,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,IAAI,YAAY;QACd,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,KAAa,EACb,OAKC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAE1F,MAAM,WAAW,GAAQ;YACvB,KAAK;YACL,QAAQ,EAAE,iBAAiB;YAC3B,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;YACxC,MAAM,EAAE,KAAK;SACd,CAAC;QAEF,IAAI,OAAO,EAAE,SAAS,EAAE,CAAC;YACvB,WAAW,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;QAC7C,CAAC;QAED,iDAAiD;QACjD,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvD,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjD,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B;aACF,CAAC,CAAC,CAAC;YACJ,WAAW,CAAC,WAAW,GAAG,MAAM,CAAC;QACnC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAExC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;YAC3C,MAAM,aAAa,GAAmB,EAAE,CAAC;YAEzC,wBAAwB;YACxB,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC9B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;oBACjD,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;wBACjC,MAAM,YAAY,GAAiB;4BACjC,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;4BAC5B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;yBACnD,CAAC;wBAEF,IAAI,CAAC;4BACH,YAAY,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;wBACrE,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,YAAY,CAAC,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAC9E,CAAC;wBAED,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,KAAK,EAAE;oBACL,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;oBACpD,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;oBACzD,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC;iBACpD;gBACD,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,UAAU;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAClG,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;CACF"}