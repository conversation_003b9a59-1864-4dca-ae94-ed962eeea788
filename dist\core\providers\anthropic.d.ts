/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { BaseLLMProvider, LLMProviderConfig } from './base.js';
import { ChatMessage, LLMResponse, BuiltinFunction } from '../types/index.js';
export declare class AnthropicProvider extends BaseLLMProvider {
    private client;
    constructor(config: LLMProviderConfig);
    get providerId(): string;
    get providerName(): string;
    validateConfig(): Promise<boolean>;
    chat(messages: ChatMessage[], model: string, options?: {
        temperature?: number;
        maxTokens?: number;
        systemPrompt?: string;
        functions?: BuiltinFunction[];
    }): Promise<LLMResponse>;
}
//# sourceMappingURL=anthropic.d.ts.map