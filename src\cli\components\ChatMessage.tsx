/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { ChatMessage as IChatMessage, Theme, FunctionCall } from '../../core/types';

interface ChatMessageProps {
  message: IChatMessage;
  theme: Theme;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, theme }) => {
  const isUser = message.role === 'user';
  const isSystem = message.role === 'system';
  
  const roleColor = isUser ? theme.colors.accent : 
                   isSystem ? theme.colors.warning : theme.colors.primary;
  
  const roleIcon = isUser ? '👤' : isSystem ? '⚙️' : '🤖';
  const roleName = isUser ? 'You' : isSystem ? 'System' : 'AI';

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderFunctionCalls = (functionCalls: FunctionCall[]) => {
    if (!functionCalls || functionCalls.length === 0) return null;

    return (
      <Box flexDirection="column" marginTop={1}>
        <Text color={theme.colors.warning} bold>
          🔧 Function Calls:
        </Text>
        {functionCalls.map((call, index) => (
          <Box key={index} marginLeft={2} marginTop={1}>
            <Box flexDirection="column">
              <Text color={theme.colors.accent}>
                📋 {call.name}({Object.keys(call.arguments).join(', ')})
              </Text>
              
              {call.result && (
                <Box marginTop={1} marginLeft={2}>
                  <Text color={theme.colors.success}>
                    ✅ Result: {typeof call.result === 'object' ? JSON.stringify(call.result, null, 2) : String(call.result)}
                  </Text>
                </Box>
              )}
              
              {call.error && (
                <Box marginTop={1} marginLeft={2}>
                  <Text color={theme.colors.error}>
                    ❌ Error: {call.error}
                  </Text>
                </Box>
              )}
            </Box>
          </Box>
        ))}
      </Box>
    );
  };

  const renderTokenUsage = () => {
    if (!message.metadata?.tokens) return null;

    const { inputTokens, outputTokens, totalTokens } = message.metadata.tokens;
    
    return (
      <Box marginTop={1}>
        <Text color={theme.colors.muted} dimColor>
          📊 Tokens: {inputTokens} in + {outputTokens} out = {totalTokens} total
        </Text>
      </Box>
    );
  };

  return (
    <Box flexDirection="column" marginBottom={2}>
      {/* Message header */}
      <Box alignItems="center" marginBottom={1}>
        <Text color={roleColor} bold>
          {roleIcon} {roleName}
        </Text>
        <Text color={theme.colors.muted}>
          {formatTimestamp(message.timestamp)}
        </Text>
        {message.metadata?.model && (
          <Text color={theme.colors.muted}>
            • {message.metadata.model}
          </Text>
        )}
      </Box>

      {/* Message content */}
      <Box 
        borderStyle="round" 
        borderColor={isUser ? theme.colors.accent : theme.colors.primary}
        padding={1}
        marginLeft={isUser ? 4 : 0}
        marginRight={isUser ? 0 : 4}
      >
        <Box flexDirection="column">
          <Text color={theme.colors.text}>
            {message.content}
          </Text>
          
          {/* Function calls */}
          {message.metadata?.functionCalls && renderFunctionCalls(message.metadata.functionCalls)}
          
          {/* Token usage */}
          {!isUser && renderTokenUsage()}
        </Box>
      </Box>
    </Box>
  );
};
