{"version": 3, "file": "AuthScreen.js", "sourceRoot": "", "sources": ["../../../src/cli/screens/AuthScreen.tsx"], "names": [], "mappings": ";;;;;;;AAAA;;;;GAIG;AAEH,iCAAmD;AACnD,6BAA0C;AAC1C,wEAA2C;AAC3C,oEAAuC;AACvC,iDAA8C;AAC9C,2DAAwD;AACxD,qCAAuC;AAiBhC,MAAM,UAAU,GAA8B,CAAC,EACpD,eAAe,EACf,OAAO,EACP,UAAU,EACX,EAAE,EAAE;IACH,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAW;QACnD,IAAI,EAAE,UAAU;QAChB,YAAY,EAAE,EAAE;QAChB,kBAAkB,EAAE,CAAC;KACtB,CAAC,CAAC;IAEH,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,IAAA,gBAAQ,EAAC,EAAE,CAAC,CAAC;IAEjD,qBAAqB;IACrB,MAAM,aAAa,GAAG,qBAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC/C,KAAK,EAAE,GAAG,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,MAAM,CAAC,MAAM,UAAU;QAC5D,KAAK,EAAE,QAAQ,CAAC,EAAE;KACnB,CAAC,CAAC,CAAC;IAEJ,MAAM,oBAAoB,GAAG,CAAC,IAAuB,EAAE,EAAE;QACvD,MAAM,QAAQ,GAAG,qBAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,IAAI,QAAQ,EAAE,CAAC;YACb,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpB,GAAG,IAAI;gBACP,IAAI,EAAE,OAAO;gBACb,gBAAgB,EAAE,QAAQ;aAC3B,CAAC,CAAC,CAAC;QACN,CAAC;IACH,CAAC,CAAC;IAEF,kBAAkB;IAClB,MAAM,UAAU,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAClE,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC,WAAW,EAAE;QAC7C,KAAK,EAAE,KAAK,CAAC,EAAE;KAChB,CAAC,CAAC,IAAI,EAAE,CAAC;IAEV,MAAM,iBAAiB,GAAG,CAAC,IAAuB,EAAE,EAAE;QACpD,MAAM,KAAK,GAAG,SAAS,CAAC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;QAChF,IAAI,KAAK,EAAE,CAAC;YACV,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpB,GAAG,IAAI;gBACP,IAAI,EAAE,QAAQ;gBACd,aAAa,EAAE,KAAK;gBACpB,kBAAkB,EAAE,CAAC;aACtB,CAAC,CAAC,CAAC;QACN,CAAC;IACH,CAAC,CAAC;IAEF,sBAAsB;IACtB,MAAM,kBAAkB,GAAG,SAAS,CAAC,gBAAgB,EAAE,YAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;IAElG,MAAM,iBAAiB,GAAG,GAAG,EAAE;QAC7B,IAAI,CAAC,kBAAkB;YAAE,OAAO;QAEhC,MAAM,eAAe,GAAG;YACtB,GAAG,SAAS,CAAC,YAAY;YACzB,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,UAAU;SACrC,CAAC;QAEF,MAAM,SAAS,GAAG,SAAS,CAAC,kBAAkB,GAAG,CAAC,CAAC;QACnD,MAAM,aAAa,GAAG,SAAS,CAAC,gBAAgB,IAAI,SAAS,GAAG,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC;QAE/G,IAAI,aAAa,EAAE,CAAC;YAClB,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpB,GAAG,IAAI;gBACP,YAAY,EAAE,eAAe;gBAC7B,kBAAkB,EAAE,SAAS;aAC9B,CAAC,CAAC,CAAC;YACJ,aAAa,CAAC,EAAE,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,oDAAoD;YACpD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACpB,GAAG,IAAI;gBACP,IAAI,EAAE,gBAAgB;gBACtB,YAAY,EAAE,eAAe;aAC9B,CAAC,CAAC,CAAC;YACJ,oBAAoB,CAAC,eAAe,CAAC,CAAC;QACxC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,KAAK,EAAE,YAAoC,EAAE,EAAE;QAC1E,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,aAAa;YAAE,OAAO;QAEpE,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG;gBACjB,UAAU,EAAE,SAAS,CAAC,gBAAgB,CAAC,EAAE;gBACzC,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,WAAW,EAAE,YAAY,CAAC,WAAW;gBACrC,gBAAgB,EAAE,YAAY;aAC/B,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,gBAAS,CAAC,oBAAoB,CAClD,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAC7B,UAAU,CACX,CAAC;YAEF,IAAI,OAAO,EAAE,CAAC;gBACZ,eAAe,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,EAAE,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;YAC7E,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,uDAAuD,CAAC,CAAC;gBACjE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC3F,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7E,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC;IAEF,wCAAwC;IACxC,IAAA,cAAQ,EAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,SAAS,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAC9C,iBAAiB,EAAE,CAAC;QACtB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,iBAAiB,GAAG,GAAG,EAAE;QAC7B,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,UAAU;gBACb,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,0CAEhB,EACP,uBAAC,0BAAW,IAAC,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,oBAAoB,GAAI,EACrE,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,2EAEpB,GACH,IACF,CACP,CAAC;YAEJ,KAAK,OAAO;gBACV,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,wCACH,SAAS,CAAC,gBAAgB,EAAE,IAAI,SAC7C,EACP,uBAAC,0BAAW,IAAC,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,iBAAiB,GAAI,EAC/D,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,2EAEpB,GACH,IACF,CACP,CAAC;YAEJ,KAAK,QAAQ;gBACX,IAAI,CAAC,kBAAkB;oBAAE,OAAO,IAAI,CAAC;gBAErC,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,iCACV,SAAS,CAAC,gBAAgB,EAAE,IAAI,SACtC,EAEP,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,wBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,aACjB,kBAAkB,CAAC,KAAK,EACxB,kBAAkB,CAAC,QAAQ,IAAI,uBAAC,UAAI,IAAC,KAAK,EAAC,KAAK,mBAAU,IACtD,GACH,EAEL,kBAAkB,CAAC,WAAW,IAAI,CACjC,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,kBACxB,kBAAkB,CAAC,WAAW,GAC1B,GACH,CACP,EAED,wBAAC,SAAG,eACF,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,YAChB,kBAAkB,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAClD,EACP,uBAAC,wBAAS,IACR,KAAK,EAAE,UAAU,EACjB,QAAQ,EAAE,aAAa,EACvB,WAAW,EAAE,SAAS,kBAAkB,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EACjE,IAAI,EAAE,kBAAkB,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,GAC9D,IACE,EAEN,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,gDACC,SAAS,CAAC,kBAAkB,GAAG,CAAC,OAAG,SAAS,CAAC,gBAAgB,EAAE,YAAY,CAAC,MAAM,SACvG,GACH,IACF,CACP,CAAC;YAEJ,KAAK,gBAAgB;gBACnB,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,aAC7C,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,2CACA,SAAS,CAAC,gBAAgB,EAAE,IAAI,WAChD,EACP,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,mEAEpB,IACH,CACP,CAAC;YAEJ;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aACpC,uBAAC,eAAM,IACL,KAAK,EAAC,UAAU,EAChB,QAAQ,EAAC,yBAAyB,EAClC,QAAQ,EAAE,IAAI,GACd,EAEF,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACd,iBAAiB,EAAE,GAChB,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAjOW,QAAA,UAAU,cAiOrB"}