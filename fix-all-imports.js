import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, extname, dirname } from 'path';

const filesToFix = [
  'src/cli/components/ChatMessage.tsx',
  'src/cli/components/Header.tsx', 
  'src/cli/components/StatusBar.tsx',
  'src/cli/components/ThemePreview.tsx',
  'src/cli/screens/AuthScreen.tsx',
  'src/cli/screens/ChatScreen.tsx',
  'src/cli/screens/LoadingScreen.tsx',
  'src/cli/screens/ThemeSelectionScreen.tsx',
  'src/core/config/index.ts',
  'src/core/providers/base.ts',
  'src/core/providers/openai.ts',
  'src/core/providers/anthropic.ts',
  'src/core/providers/google.ts',
  'src/core/providers/deepseek.ts',
  'src/core/providers/index.ts',
  'src/core/functions/index.ts',
  'src/core/index.ts'
];

const importMappings = {
  // Core imports
  "'../core'": "'../core/index.js'",
  "'../core/types'": "'../core/types/index.js'",
  "'../../core'": "'../../core/index.js'",
  "'../../core/types'": "'../../core/types/index.js'",
  "'../../core/config/themes'": "'../../core/config/themes.js'",
  "'../../core/config/providers'": "'../../core/config/providers.js'",

  // Component imports
  "'./components/ErrorBoundary'": "'./components/ErrorBoundary.js'",
  "'./components/Header'": "'./components/Header.js'",
  "'./components/ThemePreview'": "'./components/ThemePreview.js'",
  "'./components/ChatMessage'": "'./components/ChatMessage.js'",
  "'./components/StatusBar'": "'./components/StatusBar.js'",
  "'../components/Header'": "'../components/Header.js'",
  "'../components/ThemePreview'": "'../components/ThemePreview.js'",
  "'../components/ChatMessage'": "'../components/ChatMessage.js'",
  "'../components/StatusBar'": "'../components/StatusBar.js'",
  
  // Screen imports
  "'./screens/AuthScreen'": "'./screens/AuthScreen.js'",
  "'./screens/ThemeSelectionScreen'": "'./screens/ThemeSelectionScreen.js'",
  "'./screens/ChatScreen'": "'./screens/ChatScreen.js'",
  "'./screens/LoadingScreen'": "'./screens/LoadingScreen.js'",
  
  // Provider imports
  "'./base'": "'./base.js'",
  "'./openai'": "'./openai.js'",
  "'./anthropic'": "'./anthropic.js'",
  "'./google'": "'./google.js'",
  "'./deepseek'": "'./deepseek.js'",
  
  // Function imports
  "'./system'": "'./system.js'",
  "'./web'": "'./web.js'",
  "'./file'": "'./file.js'",
  "'./utility'": "'./utility.js'",
  
  // Config imports
  "'./providers'": "'./providers.js'",
  "'./themes'": "'./themes.js'",
  "'../types'": "'../types/index.js'",
  "'../config'": "'../config/index.js'",
  "'../providers'": "'../providers/index.js'",
  "'../functions'": "'../functions/index.js'"
};

function fixImportsInFile(filePath) {
  try {
    let content = readFileSync(filePath, 'utf8');
    let changed = false;
    
    for (const [oldImport, newImport] of Object.entries(importMappings)) {
      if (content.includes(oldImport)) {
        content = content.replace(new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newImport);
        changed = true;
      }
    }
    
    if (changed) {
      writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed imports in: ${filePath}`);
    } else {
      console.log(`⏭️  No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.log(`❌ Error processing ${filePath}:`, error.message);
  }
}

console.log('🔧 Fixing import paths for ES modules...\n');

for (const file of filesToFix) {
  fixImportsInFile(file);
}

console.log('\n✅ Import fixing complete!');
