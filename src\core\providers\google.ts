/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { GoogleGenerativeAI } from '@google/generative-ai';
import { Base<PERSON><PERSON>rovider, LLMProviderConfig } from './base.js';
import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';

export class GoogleProvider extends BaseLLMProvider {
  private client: GoogleGenerativeAI;

  constructor(config: LLMProviderConfig) {
    super(config);
    if (!config.apiKey) {
      throw new Error('Google API key is required');
    }
    this.client = new GoogleGenerativeAI(config.apiKey);
  }

  get providerId(): string {
    return 'google';
  }

  get providerName(): string {
    return 'Google Gemini';
  }

  async validateConfig(): Promise<boolean> {
    try {
      const model = this.client.getGenerativeModel({ model: 'gemini-1.5-flash' });
      await model.generateContent('Hi');
      return true;
    } catch (error) {
      return false;
    }
  }

  async chat(
    messages: ChatMessage[],
    model: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      functions?: BuiltinFunction[];
    }
  ): Promise<LLMResponse> {
    const generativeModel = this.client.getGenerativeModel({
      model,
      generationConfig: {
        temperature: options?.temperature ?? 0.7,
        maxOutputTokens: options?.maxTokens
      },
      systemInstruction: options?.systemPrompt
    });

    // Convert messages to Google's format
    const history = messages.slice(0, -1).map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    }));

    const lastMessage = messages[messages.length - 1];
    if (!lastMessage) {
      throw new Error('No messages provided');
    }

    try {
      let chat;
      if (history.length > 0) {
        chat = generativeModel.startChat({ history });
      } else {
        chat = generativeModel.startChat();
      }

      const result = await chat.sendMessage(lastMessage.content);
      const response = result.response;
      
      const content = response.text();
      const functionCalls: FunctionCall[] = [];

      // Handle function calls if they exist
      const functionCallCandidates = response.functionCalls();
      if (functionCallCandidates) {
        for (const functionCall of functionCallCandidates) {
          const call: FunctionCall = {
            id: this.generateId(),
            name: functionCall.name,
            arguments: functionCall.args
          };

          try {
            call.result = await this.executeFunctionCall(call);
          } catch (error) {
            call.error = error instanceof Error ? error.message : String(error);
          }

          functionCalls.push(call);
        }
      }

      // Estimate token usage (Google doesn't provide exact counts in all cases)
      const inputTokens = this.calculateTokens(messages.map(m => m.content).join(' '));
      const outputTokens = this.calculateTokens(content);

      return {
        content,
        functionCalls,
        usage: {
          inputTokens,
          outputTokens,
          totalTokens: inputTokens + outputTokens
        },
        model,
        provider: this.providerId
      };
    } catch (error) {
      throw new Error(`Google API error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
