{"version": 3, "file": "StatusBar.js", "sourceRoot": "", "sources": ["../../../src/cli/components/StatusBar.tsx"], "names": [], "mappings": ";;;;AAOA,6BAAgC;AAWzB,MAAM,SAAS,GAA6B,CAAC,EAClD,QAAQ,EACR,KAAK,EACL,KAAK,EACL,YAAY,EACZ,QAAQ,EACT,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,OAAO,IAAI,IAAI,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE;YAC5C,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,SAAS;YACjB,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,CACL,wBAAC,SAAG,IACF,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAC/B,QAAQ,EAAE,CAAC,EACX,cAAc,EAAC,eAAe,EAC9B,UAAU,EAAC,QAAQ,aAGnB,wBAAC,SAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,oCACjC,QAAQ,CAAC,WAAW,EAAE,IACrB,EACP,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,uBAExB,EACP,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,YAC3B,KAAK,GACD,IACH,EAGN,uBAAC,SAAG,IAAC,UAAU,EAAC,QAAQ,YACrB,QAAQ,CAAC,CAAC,CAAC,CACV,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,2CAE1B,CACR,CAAC,CAAC,CAAC,CACF,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,6BAE1B,CACR,GACG,EAGN,wBAAC,SAAG,IAAC,UAAU,EAAC,QAAQ,aACtB,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,8BACzB,YAAY,iBACX,EACP,uBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,uBAExB,EACP,wBAAC,UAAI,IAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,8BACzB,cAAc,EAAE,IACf,IACH,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAhEW,QAAA,SAAS,aAgEpB"}