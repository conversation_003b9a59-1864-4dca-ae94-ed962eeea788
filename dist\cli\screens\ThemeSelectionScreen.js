import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect } from 'react';
import { Box, Text, useInput } from 'ink';
import { Header } from '../components/Header.js';
import { ThemePreview } from '../components/ThemePreview.js';
import { THEMES } from '../../core/config/themes.js';
export const ThemeSelectionScreen = ({ currentTheme, onThemeSelected, onError }) => {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [previewTheme, setPreviewTheme] = useState(THEMES[0]);
    useEffect(() => {
        // Find current theme index
        const currentIndex = THEMES.findIndex(theme => theme.id === currentTheme);
        if (currentIndex !== -1) {
            setSelectedIndex(currentIndex);
            setPreviewTheme(THEMES[currentIndex]);
        }
    }, [currentTheme]);
    useEffect(() => {
        setPreviewTheme(THEMES[selectedIndex]);
    }, [selectedIndex]);
    useInput((input, key) => {
        if (key.upArrow) {
            setSelectedIndex(prev => (prev > 0 ? prev - 1 : THEMES.length - 1));
        }
        else if (key.downArrow) {
            setSelectedIndex(prev => (prev < THEMES.length - 1 ? prev + 1 : 0));
        }
        else if (key.return) {
            onThemeSelected(THEMES[selectedIndex].id);
        }
        else if (key.escape) {
            // Skip theme selection and use current theme
            onThemeSelected(currentTheme);
        }
    });
    return (_jsxs(Box, { flexDirection: "column", padding: 2, children: [_jsx(Header, { title: "THEME SELECTION", subtitle: "Choose your terminal appearance", theme: previewTheme, showLogo: false }), _jsxs(Box, { marginTop: 2, flexDirection: "column", children: [_jsxs(Text, { color: previewTheme.colors.accent, bold: true, children: ["\uD83C\uDFA8 Available Themes (", selectedIndex + 1, "/", THEMES.length, ")"] }), _jsx(Box, { marginBottom: 2, children: _jsx(Text, { color: previewTheme.colors.text, children: "Use \u2191\u2193 arrows to preview themes, Enter to select, Esc to skip" }) }), _jsx(Box, { marginBottom: 2, children: _jsx(ThemePreview, { theme: previewTheme, isSelected: true, isActive: true }) }), _jsx(Box, { flexDirection: "column", children: THEMES.map((theme, index) => (_jsxs(Box, { marginBottom: 1, children: [_jsxs(Text, { color: index === selectedIndex ? previewTheme.colors.accent : previewTheme.colors.muted, bold: index === selectedIndex, children: [index === selectedIndex ? '▶ ' : '  ', theme.name, theme.id === currentTheme ? ' (current)' : ''] }), _jsx(Text, { color: previewTheme.colors.muted, dimColor: true, children: theme.description })] }, theme.id))) }), _jsx(Box, { marginTop: 2, borderStyle: "round", borderColor: previewTheme.colors.primary, padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: previewTheme.colors.text, bold: true, children: "\uD83C\uDFAF Theme Preview Features:" }), _jsx(Text, { color: previewTheme.colors.text, children: "\u2022 Live color palette visualization" }), _jsx(Text, { color: previewTheme.colors.text, children: "\u2022 Sample chat interface preview" }), _jsx(Text, { color: previewTheme.colors.text, children: "\u2022 Real-time theme switching" }), _jsx(Text, { color: previewTheme.colors.text, children: "\u2022 Customizable appearance" })] }) }), _jsx(Box, { marginTop: 2, children: _jsx(Text, { color: previewTheme.colors.warning, children: "\uD83D\uDCA1 You can change themes anytime during chat with /theme command" }) })] })] }));
};
//# sourceMappingURL=ThemeSelectionScreen.js.map