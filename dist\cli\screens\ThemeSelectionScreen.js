"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThemeSelectionScreen = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
const react_1 = require("react");
const ink_1 = require("ink");
const Header_1 = require("../components/Header");
const ThemePreview_1 = require("../components/ThemePreview");
const themes_1 = require("../../core/config/themes");
const ThemeSelectionScreen = ({ currentTheme, onThemeSelected, onError }) => {
    const [selectedIndex, setSelectedIndex] = (0, react_1.useState)(0);
    const [previewTheme, setPreviewTheme] = (0, react_1.useState)(themes_1.THEMES[0]);
    (0, react_1.useEffect)(() => {
        // Find current theme index
        const currentIndex = themes_1.THEMES.findIndex(theme => theme.id === currentTheme);
        if (currentIndex !== -1) {
            setSelectedIndex(currentIndex);
            setPreviewTheme(themes_1.THEMES[currentIndex]);
        }
    }, [currentTheme]);
    (0, react_1.useEffect)(() => {
        setPreviewTheme(themes_1.THEMES[selectedIndex]);
    }, [selectedIndex]);
    (0, ink_1.useInput)((input, key) => {
        if (key.upArrow) {
            setSelectedIndex(prev => (prev > 0 ? prev - 1 : themes_1.THEMES.length - 1));
        }
        else if (key.downArrow) {
            setSelectedIndex(prev => (prev < themes_1.THEMES.length - 1 ? prev + 1 : 0));
        }
        else if (key.return) {
            onThemeSelected(themes_1.THEMES[selectedIndex].id);
        }
        else if (key.escape) {
            // Skip theme selection and use current theme
            onThemeSelected(currentTheme);
        }
    });
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", padding: 2, children: [(0, jsx_runtime_1.jsx)(Header_1.Header, { title: "THEME SELECTION", subtitle: "Choose your terminal appearance", theme: previewTheme, showLogo: false }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 2, flexDirection: "column", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: previewTheme.colors.accent, bold: true, children: ["\uD83C\uDFA8 Available Themes (", selectedIndex + 1, "/", themes_1.THEMES.length, ")"] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.text, children: "Use \u2191\u2193 arrows to preview themes, Enter to select, Esc to skip" }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsx)(ThemePreview_1.ThemePreview, { theme: previewTheme, isSelected: true, isActive: true }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", children: themes_1.THEMES.map((theme, index) => ((0, jsx_runtime_1.jsxs)(ink_1.Box, { marginBottom: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: index === selectedIndex ? previewTheme.colors.accent : previewTheme.colors.muted, bold: index === selectedIndex, children: [index === selectedIndex ? '▶ ' : '  ', theme.name, theme.id === currentTheme ? ' (current)' : ''] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.muted, dimColor: true, children: theme.description })] }, theme.id))) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, borderStyle: "round", borderColor: previewTheme.colors.primary, padding: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.text, bold: true, children: "\uD83C\uDFAF Theme Preview Features:" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.text, children: "\u2022 Live color palette visualization" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.text, children: "\u2022 Sample chat interface preview" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.text, children: "\u2022 Real-time theme switching" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.text, children: "\u2022 Customizable appearance" })] }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: previewTheme.colors.warning, children: "\uD83D\uDCA1 You can change themes anytime during chat with /theme command" }) })] })] }));
};
exports.ThemeSelectionScreen = ThemeSelectionScreen;
//# sourceMappingURL=ThemeSelectionScreen.js.map