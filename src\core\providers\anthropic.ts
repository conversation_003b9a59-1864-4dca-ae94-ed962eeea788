/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import Anthropic from '@anthropic-ai/sdk';
import { Base<PERSON><PERSON>rovider, LLMProviderConfig } from './base.js';
import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';

export class <PERSON>throp<PERSON><PERSON><PERSON><PERSON> extends BaseLLMProvider {
  private client: Anthropic;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.client = new Anthropic({
      apiKey: config.apiKey
    });
  }

  get providerId(): string {
    return 'anthropic';
  }

  get providerName(): string {
    return 'Anthropic';
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test with a minimal request
      await this.client.messages.create({
        model: 'claude-3-5-haiku-20241022',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hi' }]
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  async chat(
    messages: ChatMessage[],
    model: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      functions?: BuiltinFunction[];
    }
  ): Promise<LLMResponse> {
    // Separate system messages from conversation messages
    const conversationMessages = messages.filter(m => m.role !== 'system');
    const formattedMessages = conversationMessages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content
    }));

    const requestParams: Anthropic.MessageCreateParams = {
      model,
      max_tokens: options?.maxTokens || 4096,
      messages: formattedMessages,
      temperature: options?.temperature ?? 0.7
    };

    // Add system prompt if provided
    if (options?.systemPrompt) {
      requestParams.system = options.systemPrompt;
    }

    // Add function calling if functions are provided
    if (options?.functions && options.functions.length > 0) {
      requestParams.tools = options.functions.map(func => ({
        name: func.name,
        description: func.description,
        input_schema: func.parameters
      }));
    }

    try {
      const response = await this.client.messages.create(requestParams);
      
      let content = '';
      const functionCalls: FunctionCall[] = [];

      // Process response content
      for (const contentBlock of response.content) {
        if (contentBlock.type === 'text') {
          content += contentBlock.text;
        } else if (contentBlock.type === 'tool_use') {
          const functionCall: FunctionCall = {
            id: contentBlock.id,
            name: contentBlock.name,
            arguments: contentBlock.input as Record<string, any>
          };

          try {
            functionCall.result = await this.executeFunctionCall(functionCall);
          } catch (error) {
            functionCall.error = error instanceof Error ? error.message : String(error);
          }

          functionCalls.push(functionCall);
        }
      }

      return {
        content,
        functionCalls,
        usage: {
          inputTokens: response.usage.input_tokens,
          outputTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens
        },
        model,
        provider: this.providerId
      };
    } catch (error) {
      throw new Error(`Anthropic API error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
