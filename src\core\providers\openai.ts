/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import OpenAI from 'openai';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LLMProviderConfig } from './base.js';
import { ChatMessage, LLMResponse, FunctionCall, BuiltinFunction } from '../types/index.js';

export class OpenAIProvider extends Base<PERSON><PERSON>rovider {
  private client: OpenAI;

  constructor(config: LLMProviderConfig) {
    super(config);
    this.client = new OpenAI({
      apiKey: config.apiKey,
      organization: config.organization
    });
  }

  get providerId(): string {
    return 'openai';
  }

  get providerName(): string {
    return 'OpenAI';
  }

  async validateConfig(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      return false;
    }
  }

  async chat(
    messages: ChatMessage[],
    model: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      functions?: BuiltinFunction[];
    }
  ): Promise<LLMResponse> {
    const formattedMessages = this.formatMessagesForProvider(messages, options?.systemPrompt);
    
    const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: formattedMessages,
      temperature: options?.temperature ?? 0.7,
      max_tokens: options?.maxTokens
    };

    // Add function calling if functions are provided
    if (options?.functions && options.functions.length > 0) {
      requestParams.tools = options.functions.map(func => ({
        type: 'function',
        function: {
          name: func.name,
          description: func.description,
          parameters: func.parameters
        }
      }));
      requestParams.tool_choice = 'auto';
    }

    try {
      const response = await this.client.chat.completions.create(requestParams);
      const choice = response.choices[0];
      
      if (!choice) {
        throw new Error('No response from OpenAI');
      }

      let content = choice.message.content || '';
      const functionCalls: FunctionCall[] = [];

      // Handle function calls
      if (choice.message.tool_calls) {
        for (const toolCall of choice.message.tool_calls) {
          if (toolCall.type === 'function') {
            const functionCall: FunctionCall = {
              id: toolCall.id,
              name: toolCall.function.name,
              arguments: JSON.parse(toolCall.function.arguments)
            };

            try {
              functionCall.result = await this.executeFunctionCall(functionCall);
            } catch (error) {
              functionCall.error = error instanceof Error ? error.message : String(error);
            }

            functionCalls.push(functionCall);
          }
        }
      }

      return {
        content,
        functionCalls,
        usage: {
          inputTokens: response.usage?.prompt_tokens || 0,
          outputTokens: response.usage?.completion_tokens || 0,
          totalTokens: response.usage?.total_tokens || 0
        },
        model,
        provider: this.providerId
      };
    } catch (error) {
      throw new Error(`OpenAI API error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
