/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

// Basic test to verify core functionality works
const { arienCore } = require('./dist/core/index.js');
const { PROVIDERS } = require('./dist/core/config/providers.js');
const { THEMES } = require('./dist/core/config/themes.js');

console.log('🚀 Testing Arien AI CLI Core Functionality...\n');

// Test 1: Configuration Management
console.log('✅ Test 1: Configuration Management');
console.log('Current theme:', arienCore.getTheme());
console.log('System prompt length:', arienCore.getSystemPrompt().length);
console.log('');

// Test 2: Provider Configuration
console.log('✅ Test 2: Provider Configuration');
console.log('Available providers:', PROVIDERS.length);
PROVIDERS.forEach(provider => {
  console.log(`  - ${provider.name}: ${provider.models.length} models`);
});
console.log('');

// Test 3: Theme System
console.log('✅ Test 3: Theme System');
console.log('Available themes:', THEMES.length);
THEMES.forEach(theme => {
  console.log(`  - ${theme.name}: ${theme.description}`);
});
console.log('');

// Test 4: Function System
console.log('✅ Test 4: Function System');
const { functionManager } = require('./dist/core/functions/index.js');
const functions = functionManager.getAllFunctions();
console.log('Available functions:', functions.length);
functions.slice(0, 5).forEach(func => {
  console.log(`  - ${func.name}: ${func.description}`);
});
console.log('');

// Test 5: Basic Function Execution
console.log('✅ Test 5: Basic Function Execution');
(async () => {
  try {
    const result = await functionManager.executeFunction('calculate', { expression: '2 + 3 * 4' });
    console.log('Calculate 2 + 3 * 4:', result);
    
    const uuid = await functionManager.executeFunction('generate_uuid', { count: 1 });
    console.log('Generated UUID:', uuid.uuids[0]);
    
    const timestamp = await functionManager.executeFunction('get_timestamp', { format: 'iso' });
    console.log('Current timestamp:', timestamp.timestamp);
    
    console.log('\n🎉 All core functionality tests passed!');
    console.log('\n📋 Summary:');
    console.log(`  • ${PROVIDERS.length} LLM providers configured`);
    console.log(`  • ${THEMES.length} themes available`);
    console.log(`  • ${functions.length} built-in functions`);
    console.log('  • Configuration management working');
    console.log('  • Function execution working');
    console.log('\n🚀 Ready for CLI interface integration!');
    
  } catch (error) {
    console.error('❌ Function execution test failed:', error.message);
  }
})();
