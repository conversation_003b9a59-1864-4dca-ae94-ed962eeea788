/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import Anthropic from '@anthropic-ai/sdk';
import { BaseLLMProvider } from './base.js';
export class AnthropicProvider extends BaseLLMProvider {
    client;
    constructor(config) {
        super(config);
        this.client = new Anthropic({
            apiKey: config.apiKey
        });
    }
    get providerId() {
        return 'anthropic';
    }
    get providerName() {
        return 'Anthropic';
    }
    async validateConfig() {
        try {
            // Test with a minimal request
            await this.client.messages.create({
                model: 'claude-3-5-haiku-20241022',
                max_tokens: 1,
                messages: [{ role: 'user', content: 'Hi' }]
            });
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async chat(messages, model, options) {
        // Separate system messages from conversation messages
        const conversationMessages = messages.filter(m => m.role !== 'system');
        const formattedMessages = conversationMessages.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
        const requestParams = {
            model,
            max_tokens: options?.maxTokens || 4096,
            messages: formattedMessages,
            temperature: options?.temperature ?? 0.7
        };
        // Add system prompt if provided
        if (options?.systemPrompt) {
            requestParams.system = options.systemPrompt;
        }
        // Add function calling if functions are provided
        if (options?.functions && options.functions.length > 0) {
            requestParams.tools = options.functions.map(func => ({
                name: func.name,
                description: func.description,
                input_schema: func.parameters
            }));
        }
        try {
            const response = await this.client.messages.create(requestParams);
            let content = '';
            const functionCalls = [];
            // Process response content
            for (const contentBlock of response.content) {
                if (contentBlock.type === 'text') {
                    content += contentBlock.text;
                }
                else if (contentBlock.type === 'tool_use') {
                    const functionCall = {
                        id: contentBlock.id,
                        name: contentBlock.name,
                        arguments: contentBlock.input
                    };
                    try {
                        functionCall.result = await this.executeFunctionCall(functionCall);
                    }
                    catch (error) {
                        functionCall.error = error instanceof Error ? error.message : String(error);
                    }
                    functionCalls.push(functionCall);
                }
            }
            return {
                content,
                functionCalls,
                usage: {
                    inputTokens: response.usage.input_tokens,
                    outputTokens: response.usage.output_tokens,
                    totalTokens: response.usage.input_tokens + response.usage.output_tokens
                },
                model,
                provider: this.providerId
            };
        }
        catch (error) {
            throw new Error(`Anthropic API error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
//# sourceMappingURL=anthropic.js.map