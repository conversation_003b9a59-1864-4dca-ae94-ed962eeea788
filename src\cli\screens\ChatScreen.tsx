/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React, { useState, useEffect, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import TextInput from 'ink-text-input';
import { Header } from '../components/Header';
import { ChatMessage } from '../components/ChatMessage';
import { StatusBar } from '../components/StatusBar';
import { arienCore } from '../../core';
import { THEMES } from '../../core/config/themes';
import { ChatMessage as IChatMessage, Theme } from '../../core/types';
import { nanoid } from 'nanoid';

interface ChatScreenProps {
  provider: string;
  model: string;
  theme: string;
  onError: (error: string) => void;
  setLoading: (loading: boolean) => void;
}

interface ChatState {
  messages: IChatMessage[];
  inputValue: string;
  isTyping: boolean;
  showHelp: boolean;
  scrollPosition: number;
}

export const ChatScreen: React.FC<ChatScreenProps> = ({
  provider,
  model,
  theme: themeId,
  onError,
  setLoading
}) => {
  const [state, setState] = useState<ChatState>({
    messages: [],
    inputValue: '',
    isTyping: false,
    showHelp: false,
    scrollPosition: 0
  });

  const theme = THEMES.find(t => t.id === themeId) || THEMES[0];
  const messagesEndRef = useRef<any>();

  useEffect(() => {
    // Add welcome message
    const welcomeMessage: IChatMessage = {
      id: nanoid(),
      role: 'assistant',
      content: `Welcome to Arien AI! 🚀\n\nYou're connected to ${provider} using the ${model} model.\nType your message below or use /help for available commands.\n\nI have access to various built-in functions including:\n• System commands and file operations\n• Web search and URL fetching\n• Mathematical calculations\n• Text analysis and encoding\n• And much more!\n\nHow can I assist you today?`,
      timestamp: new Date(),
      metadata: {
        provider,
        model
      }
    };

    setState(prev => ({
      ...prev,
      messages: [welcomeMessage]
    }));
  }, [provider, model]);

  const handleSendMessage = async () => {
    if (!state.inputValue.trim() || state.isTyping) return;

    const userMessage: IChatMessage = {
      id: nanoid(),
      role: 'user',
      content: state.inputValue.trim(),
      timestamp: new Date()
    };

    // Check for commands
    if (userMessage.content.startsWith('/')) {
      handleCommand(userMessage.content);
      setState(prev => ({
        ...prev,
        inputValue: '',
        messages: [...prev.messages, userMessage]
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      inputValue: '',
      isTyping: true,
      messages: [...prev.messages, userMessage]
    }));

    try {
      setLoading(true);
      
      const response = await arienCore.chat(
        [...state.messages, userMessage],
        provider,
        model,
        {
          enableFunctions: true,
          systemPrompt: arienCore.getSystemPrompt()
        }
      );

      const assistantMessage: IChatMessage = {
        id: nanoid(),
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        metadata: {
          provider: response.provider,
          model: response.model,
          tokens: response.usage,
          functionCalls: response.functionCalls
        }
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, assistantMessage],
        isTyping: false
      }));

    } catch (error) {
      onError(`Chat error: ${error instanceof Error ? error.message : String(error)}`);
      setState(prev => ({ ...prev, isTyping: false }));
    } finally {
      setLoading(false);
    }
  };

  const handleCommand = (command: string) => {
    const [cmd, ...args] = command.slice(1).split(' ');
    
    switch (cmd.toLowerCase()) {
      case 'help':
        setState(prev => ({ ...prev, showHelp: !prev.showHelp }));
        break;
      case 'clear':
        setState(prev => ({ ...prev, messages: [] }));
        break;
      case 'theme':
        if (args.length > 0) {
          const newTheme = THEMES.find(t => t.id === args[0] || t.name.toLowerCase() === args.join(' ').toLowerCase());
          if (newTheme) {
            arienCore.setTheme(newTheme.id);
            // Note: This would require a prop to update theme in parent component
          }
        }
        break;
      case 'history':
        const history = arienCore.getChatHistory();
        console.log('Chat history:', history);
        break;
      case 'status':
        console.log('Current provider:', provider);
        console.log('Current model:', model);
        console.log('Current theme:', themeId);
        break;
      default:
        onError(`Unknown command: /${cmd}. Type /help for available commands.`);
    }
  };

  useInput((input, key) => {
    if (key.return && !key.shift) {
      handleSendMessage();
    } else if (key.escape) {
      setState(prev => ({ ...prev, showHelp: false }));
    } else if (key.ctrl && input === 'c') {
      process.exit(0);
    }
  });

  return (
    <Box flexDirection="column" height="100%">
      {/* Header */}
      <Header 
        title={`${provider.toUpperCase()} • ${model}`}
        subtitle="AI-Powered Terminal Chat"
        theme={theme}
        showLogo={false}
      />

      {/* Help panel */}
      {state.showHelp && (
        <Box 
          borderStyle="round" 
          borderColor={theme.colors.accent}
          margin={1}
          padding={1}
        >
          <Box flexDirection="column">
            <Text color={theme.colors.accent} bold>
              📚 Available Commands:
            </Text>
            <Text color={theme.colors.text}>
              /help - Toggle this help panel
            </Text>
            <Text color={theme.colors.text}>
              /clear - Clear chat history
            </Text>
            <Text color={theme.colors.text}>
              /theme [name] - Change theme
            </Text>
            <Text color={theme.colors.text}>
              /history - Show chat history
            </Text>
            <Text color={theme.colors.text}>
              /status - Show current status
            </Text>
            <Text color={theme.colors.muted}>
              Press Esc to close, Ctrl+C to exit
            </Text>
          </Box>
        </Box>
      )}

      {/* Chat messages */}
      <Box flexDirection="column" flexGrow={1} padding={1}>
        {state.messages.map((message) => (
          <ChatMessage 
            key={message.id}
            message={message}
            theme={theme}
          />
        ))}
        
        {state.isTyping && (
          <Box marginTop={1}>
            <Text color={theme.colors.muted}>
              🤖 AI is thinking...
            </Text>
          </Box>
        )}
        
        <div ref={messagesEndRef} />
      </Box>

      {/* Input area */}
      <Box 
        borderStyle="round" 
        borderColor={theme.colors.primary}
        margin={1}
        padding={1}
      >
        <Box alignItems="center" width="100%">
          <Text color={theme.colors.accent}>
            💬
          </Text>
          <Box flexGrow={1}>
            <TextInput
              value={state.inputValue}
              onChange={(value) => setState(prev => ({ ...prev, inputValue: value }))}
              placeholder="Type your message... (Enter to send, /help for commands)"
            />
          </Box>
        </Box>
      </Box>

      {/* Status bar */}
      <StatusBar 
        provider={provider}
        model={model}
        theme={theme}
        messageCount={state.messages.length}
        isTyping={state.isTyping}
      />
    </Box>
  );
};
