{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "bundler", "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": false, "resolveJsonModule": true, "jsx": "react-jsx", "types": ["node"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@cli/*": ["./cli/*"], "@core/*": ["./core/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}