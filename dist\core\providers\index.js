/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { OpenAIProvider } from './openai.js';
import { AnthropicProvider } from './anthropic.js';
import { GoogleProvider } from './google.js';
import { DeepSeekProvider } from './deepseek.js';
export class ProviderManager {
    providers = new Map();
    createProvider(providerId, config) {
        const providerConfig = {
            apiKey: config.apiKey,
            accessToken: config.accessToken,
            ...config.additionalConfig
        };
        let provider;
        switch (providerId) {
            case 'openai':
                provider = new OpenAIProvider(providerConfig);
                break;
            case 'anthropic':
                provider = new AnthropicProvider(providerConfig);
                break;
            case 'google':
                provider = new GoogleProvider(providerConfig);
                break;
            case 'deepseek':
                provider = new DeepSeekProvider(providerConfig);
                break;
            default:
                throw new Error(`Unknown provider: ${providerId}`);
        }
        this.providers.set(providerId, provider);
        return provider;
    }
    getProvider(providerId) {
        return this.providers.get(providerId);
    }
    hasProvider(providerId) {
        return this.providers.has(providerId);
    }
    removeProvider(providerId) {
        this.providers.delete(providerId);
    }
    getAllProviders() {
        return new Map(this.providers);
    }
    async validateProvider(providerId) {
        const provider = this.getProvider(providerId);
        if (!provider) {
            return false;
        }
        try {
            return await provider.validateConfig();
        }
        catch (error) {
            return false;
        }
    }
}
export const providerManager = new ProviderManager();
export * from './base.js';
export * from './openai.js';
export * from './anthropic.js';
export * from './google.js';
export * from './deepseek.js';
//# sourceMappingURL=index.js.map