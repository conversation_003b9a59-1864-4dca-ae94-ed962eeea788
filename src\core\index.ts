/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

export * from './types/index.js';
export * from './config/index.js';
export * from './providers/index.js';
export * from './functions/index.js';

import { configManager } from './config/index.js';
import { providerManager } from './providers/index.js';
import { functionManager } from './functions/index.js';
import { ChatMessage, LLMResponse, AuthConfig } from './types/index.js';

export class ArienCore {
  constructor() {
    // Initialize built-in functions for all providers
    this.initializeProviders();
  }

  private initializeProviders(): void {
    // Set built-in functions for existing providers
    const providers = providerManager.getAllProviders();
    const functions = functionManager.getAllFunctions();
    
    for (const [, provider] of providers) {
      provider.setBuiltinFunctions(functions);
    }
  }

  async authenticateProvider(providerId: string, authConfig: AuthConfig): Promise<boolean> {
    try {
      // Create provider instance
      const provider = providerManager.createProvider(providerId, authConfig);
      
      // Set built-in functions
      provider.setBuiltinFunctions(functionManager.getAllFunctions());
      
      // Validate configuration
      const isValid = await provider.validateConfig();
      
      if (isValid) {
        // Save authentication config
        configManager.setAuthConfig(providerId, authConfig);
        return true;
      } else {
        // Remove provider if validation failed
        providerManager.removeProvider(providerId);
        return false;
      }
    } catch (error) {
      console.error(`Authentication failed for ${providerId}:`, error);
      return false;
    }
  }

  async chat(
    messages: ChatMessage[],
    providerId?: string,
    modelId?: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
      enableFunctions?: boolean;
    }
  ): Promise<LLMResponse> {
    // Use current provider/model if not specified
    const currentProvider = providerId || configManager.getCurrentProvider();
    const currentModel = modelId || configManager.getCurrentModel();
    
    if (!currentProvider || !currentModel) {
      throw new Error('No provider or model selected');
    }

    // Get provider instance
    let provider = providerManager.getProvider(currentProvider);
    
    if (!provider) {
      // Try to create provider from saved auth config
      const authConfig = configManager.getAuthConfig(currentProvider);
      if (!authConfig) {
        throw new Error(`Provider ${currentProvider} not authenticated`);
      }
      
      provider = providerManager.createProvider(currentProvider, authConfig);
      provider.setBuiltinFunctions(functionManager.getAllFunctions());
    }

    // Prepare chat options
    const chatOptions = {
      temperature: options?.temperature,
      maxTokens: options?.maxTokens,
      systemPrompt: options?.systemPrompt || configManager.getSystemPrompt(),
      functions: options?.enableFunctions !== false ? functionManager.getAllFunctions() : undefined
    };

    // Execute chat
    return await provider.chat(messages, currentModel, chatOptions);
  }

  isProviderAuthenticated(providerId: string): boolean {
    return configManager.isProviderAuthenticated(providerId);
  }

  getCurrentProvider(): string | undefined {
    return configManager.getCurrentProvider();
  }

  getCurrentModel(): string | undefined {
    return configManager.getCurrentModel();
  }

  setCurrentProvider(providerId: string, modelId?: string): void {
    configManager.setCurrentProvider(providerId, modelId);
  }

  getTheme(): string {
    return configManager.getTheme();
  }

  setTheme(themeId: string): void {
    configManager.setTheme(themeId);
  }

  getSystemPrompt(): string {
    return configManager.getSystemPrompt();
  }

  setSystemPrompt(prompt: string): void {
    configManager.setSystemPrompt(prompt);
  }

  addChatSession(session: any): void {
    configManager.addChatSession(session);
  }

  getChatHistory(): any[] {
    return configManager.getChatHistory();
  }

  clearChatHistory(): void {
    configManager.clearChatHistory();
  }

  reset(): void {
    configManager.reset();
    providerManager.getAllProviders().clear();
  }
}

export const arienCore = new ArienCore();
