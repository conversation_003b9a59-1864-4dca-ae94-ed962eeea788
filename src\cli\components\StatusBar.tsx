/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import React from 'react';
import { Box, Text } from 'ink';
import { Theme } from '../../core/types/index.js';

interface StatusBarProps {
  provider: string;
  model: string;
  theme: Theme;
  messageCount: number;
  isTyping: boolean;
}

export const StatusBar: React.FC<StatusBarProps> = ({
  provider,
  model,
  theme,
  messageCount,
  isTyping
}) => {
  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <Box 
      borderStyle="single" 
      borderColor={theme.colors.muted}
      paddingX={1}
      justifyContent="space-between"
      alignItems="center"
    >
      {/* Left side - Provider info */}
      <Box alignItems="center">
        <Text color={theme.colors.primary} bold>
          🔗 {provider.toUpperCase()}
        </Text>
        <Text color={theme.colors.muted}>
          •
        </Text>
        <Text color={theme.colors.text}>
          {model}
        </Text>
      </Box>

      {/* Center - Status */}
      <Box alignItems="center">
        {isTyping ? (
          <Text color={theme.colors.warning}>
            ⏳ AI is responding...
          </Text>
        ) : (
          <Text color={theme.colors.success}>
            ✅ Ready
          </Text>
        )}
      </Box>

      {/* Right side - Stats and time */}
      <Box alignItems="center">
        <Text color={theme.colors.muted}>
          💬 {messageCount} messages
        </Text>
        <Text color={theme.colors.muted}>
          •
        </Text>
        <Text color={theme.colors.muted}>
          🕒 {getCurrentTime()}
        </Text>
      </Box>
    </Box>
  );
};
