"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMessage = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const ink_1 = require("ink");
const ChatMessage = ({ message, theme }) => {
    const isUser = message.role === 'user';
    const isSystem = message.role === 'system';
    const roleColor = isUser ? theme.colors.accent :
        isSystem ? theme.colors.warning : theme.colors.primary;
    const roleIcon = isUser ? '👤' : isSystem ? '⚙️' : '🤖';
    const roleName = isUser ? 'You' : isSystem ? 'System' : 'AI';
    const formatTimestamp = (timestamp) => {
        return timestamp.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
    };
    const renderFunctionCalls = (functionCalls) => {
        if (!functionCalls || functionCalls.length === 0)
            return null;
        return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginTop: 1, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.warning, bold: true, children: "\uD83D\uDD27 Function Calls:" }), functionCalls.map((call, index) => ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginLeft: 2, marginTop: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.accent, children: ["\uD83D\uDCCB ", call.name, "(", Object.keys(call.arguments).join(', '), ")"] }), call.result && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, marginLeft: 2, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.success, children: ["\u2705 Result: ", typeof call.result === 'object' ? JSON.stringify(call.result, null, 2) : String(call.result)] }) })), call.error && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, marginLeft: 2, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.error, children: ["\u274C Error: ", call.error] }) }))] }) }, index)))] }));
    };
    const renderTokenUsage = () => {
        if (!message.metadata?.tokens)
            return null;
        const { inputTokens, outputTokens, totalTokens } = message.metadata.tokens;
        return ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.muted, dimColor: true, children: ["\uD83D\uDCCA Tokens: ", inputTokens, " in + ", outputTokens, " out = ", totalTokens, " total"] }) }));
    };
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { alignItems: "center", marginBottom: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: roleColor, bold: true, children: [roleIcon, " ", roleName] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.muted, children: formatTimestamp(message.timestamp) }), message.metadata?.model && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: theme.colors.muted, children: ["\u2022 ", message.metadata.model] }))] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { borderStyle: "round", borderColor: isUser ? theme.colors.accent : theme.colors.primary, padding: 1, marginLeft: isUser ? 4 : 0, marginRight: isUser ? 0 : 4, children: (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: message.content }), message.metadata?.functionCalls && renderFunctionCalls(message.metadata.functionCalls), !isUser && renderTokenUsage()] }) })] }));
};
exports.ChatMessage = ChatMessage;
//# sourceMappingURL=ChatMessage.js.map