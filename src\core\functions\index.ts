/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { BuiltinFunction } from '../types/index.js';
import { systemFunctions } from './system.js';
import { webFunctions } from './web.js';
import { fileFunctions } from './file.js';
import { utilityFunctions } from './utility.js';

export class FunctionManager {
  private functions: Map<string, BuiltinFunction> = new Map();

  constructor() {
    this.registerDefaultFunctions();
  }

  private registerDefaultFunctions(): void {
    // Register all built-in function categories
    [...systemFunctions, ...webFunctions, ...fileFunctions, ...utilityFunctions].forEach(func => {
      this.functions.set(func.name, func);
    });
  }

  registerFunction(func: BuiltinFunction): void {
    this.functions.set(func.name, func);
  }

  unregisterFunction(name: string): void {
    this.functions.delete(name);
  }

  getFunction(name: string): BuiltinFunction | undefined {
    return this.functions.get(name);
  }

  getAllFunctions(): BuiltinFunction[] {
    return Array.from(this.functions.values());
  }

  getFunctionsByCategory(category: string): BuiltinFunction[] {
    return this.getAllFunctions().filter(func => 
      func.description.toLowerCase().includes(category.toLowerCase())
    );
  }

  async executeFunction(name: string, args: Record<string, any>): Promise<any> {
    const func = this.getFunction(name);
    if (!func) {
      throw new Error(`Function '${name}' not found`);
    }

    try {
      return await func.handler(args);
    } catch (error) {
      throw new Error(`Function '${name}' execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  validateFunctionCall(name: string, args: Record<string, any>): { valid: boolean; error?: string } {
    const func = this.getFunction(name);
    if (!func) {
      return { valid: false, error: `Function '${name}' not found` };
    }

    // Check required parameters
    const required = func.parameters.required || [];
    for (const param of required) {
      if (!(param in args)) {
        return { valid: false, error: `Missing required parameter: ${param}` };
      }
    }

    // Basic type validation
    const properties = func.parameters.properties || {};
    for (const [key, value] of Object.entries(args)) {
      const propSchema = properties[key];
      if (propSchema) {
        const expectedType = propSchema.type;
        const actualType = typeof value;
        
        if (expectedType === 'string' && actualType !== 'string') {
          return { valid: false, error: `Parameter '${key}' must be a string` };
        }
        if (expectedType === 'number' && actualType !== 'number') {
          return { valid: false, error: `Parameter '${key}' must be a number` };
        }
        if (expectedType === 'boolean' && actualType !== 'boolean') {
          return { valid: false, error: `Parameter '${key}' must be a boolean` };
        }
        if (expectedType === 'array' && !Array.isArray(value)) {
          return { valid: false, error: `Parameter '${key}' must be an array` };
        }
      }
    }

    return { valid: true };
  }
}

export const functionManager = new FunctionManager();

export * from './system.js';
export * from './web.js';
export * from './file.js';
export * from './utility.js';
