/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { GoogleGenerativeAI } from '@google/generative-ai';
import { BaseLLMProvider } from './base.js';
export class GoogleProvider extends BaseLLMProvider {
    client;
    constructor(config) {
        super(config);
        if (!config.apiKey) {
            throw new Error('Google API key is required');
        }
        this.client = new GoogleGenerativeAI(config.apiKey);
    }
    get providerId() {
        return 'google';
    }
    get providerName() {
        return 'Google Gemini';
    }
    async validateConfig() {
        try {
            const model = this.client.getGenerativeModel({ model: 'gemini-1.5-flash' });
            await model.generateContent('Hi');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    async chat(messages, model, options) {
        const generativeModel = this.client.getGenerativeModel({
            model,
            generationConfig: {
                temperature: options?.temperature ?? 0.7,
                maxOutputTokens: options?.maxTokens
            },
            systemInstruction: options?.systemPrompt
        });
        // Convert messages to Google's format
        const history = messages.slice(0, -1).map(msg => ({
            role: msg.role === 'assistant' ? 'model' : 'user',
            parts: [{ text: msg.content }]
        }));
        const lastMessage = messages[messages.length - 1];
        if (!lastMessage) {
            throw new Error('No messages provided');
        }
        try {
            let chat;
            if (history.length > 0) {
                chat = generativeModel.startChat({ history });
            }
            else {
                chat = generativeModel.startChat();
            }
            const result = await chat.sendMessage(lastMessage.content);
            const response = result.response;
            const content = response.text();
            const functionCalls = [];
            // Handle function calls if they exist
            const functionCallCandidates = response.functionCalls();
            if (functionCallCandidates) {
                for (const functionCall of functionCallCandidates) {
                    const call = {
                        id: this.generateId(),
                        name: functionCall.name,
                        arguments: functionCall.args
                    };
                    try {
                        call.result = await this.executeFunctionCall(call);
                    }
                    catch (error) {
                        call.error = error instanceof Error ? error.message : String(error);
                    }
                    functionCalls.push(call);
                }
            }
            // Estimate token usage (Google doesn't provide exact counts in all cases)
            const inputTokens = this.calculateTokens(messages.map(m => m.content).join(' '));
            const outputTokens = this.calculateTokens(content);
            return {
                content,
                functionCalls,
                usage: {
                    inputTokens,
                    outputTokens,
                    totalTokens: inputTokens + outputTokens
                },
                model,
                provider: this.providerId
            };
        }
        catch (error) {
            throw new Error(`Google API error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
//# sourceMappingURL=google.js.map