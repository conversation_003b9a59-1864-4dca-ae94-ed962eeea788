/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import Conf from 'conf';
import { AppConfig, AuthConfig } from '../types/index.js';
import { DEFAULT_SYSTEM_PROMPT } from './providers.js';
import { DEFAULT_THEME_ID } from './themes.js';

export class ConfigManager {
  private config: Conf<AppConfig>;

  constructor() {
    this.config = new Conf<AppConfig>({
      projectName: 'arien-ai-cli',
      defaults: {
        theme: DEFAULT_THEME_ID,
        systemPrompt: DEFAULT_SYSTEM_PROMPT,
        authConfigs: {},
        chatHistory: [],
        preferences: {
          autoSave: true,
          showTokenCount: true,
          enableFunctionCalling: true,
          maxHistoryLength: 100
        }
      }
    });
  }

  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config.get(key);
  }

  set<K extends keyof AppConfig>(key: K, value: AppConfig[K]): void {
    this.config.set(key, value);
  }

  getAuthConfig(providerId: string): AuthConfig | undefined {
    const authConfigs = this.get('authConfigs');
    return authConfigs[providerId];
  }

  setAuthConfig(providerId: string, authConfig: AuthConfig): void {
    const authConfigs = this.get('authConfigs');
    authConfigs[providerId] = authConfig;
    this.set('authConfigs', authConfigs);
  }

  removeAuthConfig(providerId: string): void {
    const authConfigs = this.get('authConfigs');
    delete authConfigs[providerId];
    this.set('authConfigs', authConfigs);
  }

  isProviderAuthenticated(providerId: string): boolean {
    const authConfig = this.getAuthConfig(providerId);
    if (!authConfig) return false;

    // Check if API key exists
    if (authConfig.apiKey) return true;

    // Check if OAuth tokens exist and are not expired
    if (authConfig.accessToken) {
      if (!authConfig.expiresAt) return true;
      return Date.now() < authConfig.expiresAt;
    }

    return false;
  }

  getCurrentProvider(): string | undefined {
    return this.get('currentProvider');
  }

  getCurrentModel(): string | undefined {
    return this.get('currentModel');
  }

  setCurrentProvider(providerId: string, modelId?: string): void {
    this.set('currentProvider', providerId);
    if (modelId) {
      this.set('currentModel', modelId);
    }
  }

  getTheme(): string {
    return this.get('theme');
  }

  setTheme(themeId: string): void {
    this.set('theme', themeId);
  }

  getSystemPrompt(): string {
    return this.get('systemPrompt');
  }

  setSystemPrompt(prompt: string): void {
    this.set('systemPrompt', prompt);
  }

  addChatSession(session: any): void {
    const history = this.get('chatHistory');
    history.unshift(session);
    
    // Limit history length
    const maxLength = this.get('preferences').maxHistoryLength;
    if (history.length > maxLength) {
      history.splice(maxLength);
    }
    
    this.set('chatHistory', history);
  }

  getChatHistory(): any[] {
    return this.get('chatHistory');
  }

  clearChatHistory(): void {
    this.set('chatHistory', []);
  }

  reset(): void {
    this.config.clear();
  }

  getConfigPath(): string {
    return this.config.path;
  }
}

export const configManager = new ConfigManager();
