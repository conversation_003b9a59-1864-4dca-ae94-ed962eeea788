/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { BuiltinFunction } from '../types/index.js';
export declare class FunctionManager {
    private functions;
    constructor();
    private registerDefaultFunctions;
    registerFunction(func: BuiltinFunction): void;
    unregisterFunction(name: string): void;
    getFunction(name: string): BuiltinFunction | undefined;
    getAllFunctions(): BuiltinFunction[];
    getFunctionsByCategory(category: string): BuiltinFunction[];
    executeFunction(name: string, args: Record<string, any>): Promise<any>;
    validateFunctionCall(name: string, args: Record<string, any>): {
        valid: boolean;
        error?: string;
    };
}
export declare const functionManager: FunctionManager;
export * from './system.js';
export * from './web.js';
export * from './file.js';
export * from './utility.js';
//# sourceMappingURL=index.d.ts.map