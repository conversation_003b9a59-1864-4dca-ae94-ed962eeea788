import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import { useState, useEffect, useRef } from 'react';
import { Box, Text, useInput } from 'ink';
import TextInput from 'ink-text-input';
import { Header } from '../components/Header.js';
import { ChatMessage } from '../components/ChatMessage.js';
import { StatusBar } from '../components/StatusBar.js';
import { arienCore } from '../../core/index.js';
import { THEMES } from '../../core/config/themes.js';
import { nanoid } from 'nanoid';
export const ChatScreen = ({ provider, model, theme: themeId, onError, setLoading }) => {
    const [state, setState] = useState({
        messages: [],
        inputValue: '',
        isTyping: false,
        showHelp: false,
        scrollPosition: 0
    });
    const theme = THEMES.find(t => t.id === themeId) || THEMES[0];
    const messagesEndRef = useRef();
    useEffect(() => {
        // Add welcome message
        const welcomeMessage = {
            id: nanoid(),
            role: 'assistant',
            content: `Welcome to Arien AI! 🚀\n\nYou're connected to ${provider} using the ${model} model.\nType your message below or use /help for available commands.\n\nI have access to various built-in functions including:\n• System commands and file operations\n• Web search and URL fetching\n• Mathematical calculations\n• Text analysis and encoding\n• And much more!\n\nHow can I assist you today?`,
            timestamp: new Date(),
            metadata: {
                provider,
                model
            }
        };
        setState(prev => ({
            ...prev,
            messages: [welcomeMessage]
        }));
    }, [provider, model]);
    const handleSendMessage = async () => {
        if (!state.inputValue.trim() || state.isTyping)
            return;
        const userMessage = {
            id: nanoid(),
            role: 'user',
            content: state.inputValue.trim(),
            timestamp: new Date()
        };
        // Check for commands
        if (userMessage.content.startsWith('/')) {
            handleCommand(userMessage.content);
            setState(prev => ({
                ...prev,
                inputValue: '',
                messages: [...prev.messages, userMessage]
            }));
            return;
        }
        setState(prev => ({
            ...prev,
            inputValue: '',
            isTyping: true,
            messages: [...prev.messages, userMessage]
        }));
        try {
            setLoading(true);
            const response = await arienCore.chat([...state.messages, userMessage], provider, model, {
                enableFunctions: true,
                systemPrompt: arienCore.getSystemPrompt()
            });
            const assistantMessage = {
                id: nanoid(),
                role: 'assistant',
                content: response.content,
                timestamp: new Date(),
                metadata: {
                    provider: response.provider,
                    model: response.model,
                    tokens: response.usage,
                    functionCalls: response.functionCalls
                }
            };
            setState(prev => ({
                ...prev,
                messages: [...prev.messages, assistantMessage],
                isTyping: false
            }));
        }
        catch (error) {
            onError(`Chat error: ${error instanceof Error ? error.message : String(error)}`);
            setState(prev => ({ ...prev, isTyping: false }));
        }
        finally {
            setLoading(false);
        }
    };
    const handleCommand = (command) => {
        const [cmd, ...args] = command.slice(1).split(' ');
        switch (cmd.toLowerCase()) {
            case 'help':
                setState(prev => ({ ...prev, showHelp: !prev.showHelp }));
                break;
            case 'clear':
                setState(prev => ({ ...prev, messages: [] }));
                break;
            case 'theme':
                if (args.length > 0) {
                    const newTheme = THEMES.find(t => t.id === args[0] || t.name.toLowerCase() === args.join(' ').toLowerCase());
                    if (newTheme) {
                        arienCore.setTheme(newTheme.id);
                        // Note: This would require a prop to update theme in parent component
                    }
                }
                break;
            case 'history':
                const history = arienCore.getChatHistory();
                console.log('Chat history:', history);
                break;
            case 'status':
                console.log('Current provider:', provider);
                console.log('Current model:', model);
                console.log('Current theme:', themeId);
                break;
            default:
                onError(`Unknown command: /${cmd}. Type /help for available commands.`);
        }
    };
    useInput((input, key) => {
        if (key.return && !key.shift) {
            handleSendMessage();
        }
        else if (key.escape) {
            setState(prev => ({ ...prev, showHelp: false }));
        }
        else if (key.ctrl && input === 'c') {
            process.exit(0);
        }
    });
    return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Header, { title: `${provider.toUpperCase()} • ${model}`, subtitle: "AI-Powered Terminal Chat", theme: theme, showLogo: false }), state.showHelp && (_jsx(Box, { borderStyle: "round", borderColor: theme.colors.accent, margin: 1, padding: 1, children: _jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: theme.colors.accent, bold: true, children: "\uD83D\uDCDA Available Commands:" }), _jsx(Text, { color: theme.colors.text, children: "/help - Toggle this help panel" }), _jsx(Text, { color: theme.colors.text, children: "/clear - Clear chat history" }), _jsx(Text, { color: theme.colors.text, children: "/theme [name] - Change theme" }), _jsx(Text, { color: theme.colors.text, children: "/history - Show chat history" }), _jsx(Text, { color: theme.colors.text, children: "/status - Show current status" }), _jsx(Text, { color: theme.colors.muted, children: "Press Esc to close, Ctrl+C to exit" })] }) })), _jsxs(Box, { flexDirection: "column", flexGrow: 1, padding: 1, children: [state.messages.map((message) => (_jsx(ChatMessage, { message: message, theme: theme }, message.id))), state.isTyping && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: theme.colors.muted, children: "\uD83E\uDD16 AI is thinking..." }) })), _jsx("div", { ref: messagesEndRef })] }), _jsx(Box, { borderStyle: "round", borderColor: theme.colors.primary, margin: 1, padding: 1, children: _jsxs(Box, { alignItems: "center", width: "100%", children: [_jsx(Text, { color: theme.colors.accent, children: "\uD83D\uDCAC" }), _jsx(Box, { flexGrow: 1, children: _jsx(TextInput, { value: state.inputValue, onChange: (value) => setState(prev => ({ ...prev, inputValue: value })), placeholder: "Type your message... (Enter to send, /help for commands)" }) })] }) }), _jsx(StatusBar, { provider: provider, model: model, theme: theme, messageCount: state.messages.length, isTyping: state.isTyping })] }));
};
//# sourceMappingURL=ChatScreen.js.map