"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatScreen = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
const react_1 = require("react");
const ink_1 = require("ink");
const ink_text_input_1 = __importDefault(require("ink-text-input"));
const Header_1 = require("../components/Header");
const ChatMessage_1 = require("../components/ChatMessage");
const StatusBar_1 = require("../components/StatusBar");
const core_1 = require("../../core");
const themes_1 = require("../../core/config/themes");
const nanoid_1 = require("nanoid");
const ChatScreen = ({ provider, model, theme: themeId, onError, setLoading }) => {
    const [state, setState] = (0, react_1.useState)({
        messages: [],
        inputValue: '',
        isTyping: false,
        showHelp: false,
        scrollPosition: 0
    });
    const theme = themes_1.THEMES.find(t => t.id === themeId) || themes_1.THEMES[0];
    const messagesEndRef = (0, react_1.useRef)();
    (0, react_1.useEffect)(() => {
        // Add welcome message
        const welcomeMessage = {
            id: (0, nanoid_1.nanoid)(),
            role: 'assistant',
            content: `Welcome to Arien AI! 🚀\n\nYou're connected to ${provider} using the ${model} model.\nType your message below or use /help for available commands.\n\nI have access to various built-in functions including:\n• System commands and file operations\n• Web search and URL fetching\n• Mathematical calculations\n• Text analysis and encoding\n• And much more!\n\nHow can I assist you today?`,
            timestamp: new Date(),
            metadata: {
                provider,
                model
            }
        };
        setState(prev => ({
            ...prev,
            messages: [welcomeMessage]
        }));
    }, [provider, model]);
    const handleSendMessage = async () => {
        if (!state.inputValue.trim() || state.isTyping)
            return;
        const userMessage = {
            id: (0, nanoid_1.nanoid)(),
            role: 'user',
            content: state.inputValue.trim(),
            timestamp: new Date()
        };
        // Check for commands
        if (userMessage.content.startsWith('/')) {
            handleCommand(userMessage.content);
            setState(prev => ({
                ...prev,
                inputValue: '',
                messages: [...prev.messages, userMessage]
            }));
            return;
        }
        setState(prev => ({
            ...prev,
            inputValue: '',
            isTyping: true,
            messages: [...prev.messages, userMessage]
        }));
        try {
            setLoading(true);
            const response = await core_1.arienCore.chat([...state.messages, userMessage], provider, model, {
                enableFunctions: true,
                systemPrompt: core_1.arienCore.getSystemPrompt()
            });
            const assistantMessage = {
                id: (0, nanoid_1.nanoid)(),
                role: 'assistant',
                content: response.content,
                timestamp: new Date(),
                metadata: {
                    provider: response.provider,
                    model: response.model,
                    tokens: response.usage,
                    functionCalls: response.functionCalls
                }
            };
            setState(prev => ({
                ...prev,
                messages: [...prev.messages, assistantMessage],
                isTyping: false
            }));
        }
        catch (error) {
            onError(`Chat error: ${error instanceof Error ? error.message : String(error)}`);
            setState(prev => ({ ...prev, isTyping: false }));
        }
        finally {
            setLoading(false);
        }
    };
    const handleCommand = (command) => {
        const [cmd, ...args] = command.slice(1).split(' ');
        switch (cmd.toLowerCase()) {
            case 'help':
                setState(prev => ({ ...prev, showHelp: !prev.showHelp }));
                break;
            case 'clear':
                setState(prev => ({ ...prev, messages: [] }));
                break;
            case 'theme':
                if (args.length > 0) {
                    const newTheme = themes_1.THEMES.find(t => t.id === args[0] || t.name.toLowerCase() === args.join(' ').toLowerCase());
                    if (newTheme) {
                        core_1.arienCore.setTheme(newTheme.id);
                        // Note: This would require a prop to update theme in parent component
                    }
                }
                break;
            case 'history':
                const history = core_1.arienCore.getChatHistory();
                console.log('Chat history:', history);
                break;
            case 'status':
                console.log('Current provider:', provider);
                console.log('Current model:', model);
                console.log('Current theme:', themeId);
                break;
            default:
                onError(`Unknown command: /${cmd}. Type /help for available commands.`);
        }
    };
    (0, ink_1.useInput)((input, key) => {
        if (key.return && !key.shift) {
            handleSendMessage();
        }
        else if (key.escape) {
            setState(prev => ({ ...prev, showHelp: false }));
        }
        else if (key.ctrl && input === 'c') {
            process.exit(0);
        }
    });
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", children: [(0, jsx_runtime_1.jsx)(Header_1.Header, { title: `${provider.toUpperCase()} • ${model}`, subtitle: "AI-Powered Terminal Chat", theme: theme, showLogo: false }), state.showHelp && ((0, jsx_runtime_1.jsx)(ink_1.Box, { borderStyle: "round", borderColor: theme.colors.accent, margin: 1, padding: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.accent, bold: true, children: "\uD83D\uDCDA Available Commands:" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: "/help - Toggle this help panel" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: "/clear - Clear chat history" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: "/theme [name] - Change theme" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: "/history - Show chat history" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.text, children: "/status - Show current status" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.muted, children: "Press Esc to close, Ctrl+C to exit" })] }) })), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", flexGrow: 1, padding: 1, children: [state.messages.map((message) => ((0, jsx_runtime_1.jsx)(ChatMessage_1.ChatMessage, { message: message, theme: theme }, message.id))), state.isTyping && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.muted, children: "\uD83E\uDD16 AI is thinking..." }) })), (0, jsx_runtime_1.jsx)("div", { ref: messagesEndRef })] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { borderStyle: "round", borderColor: theme.colors.primary, margin: 1, padding: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Box, { alignItems: "center", width: "100%", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme.colors.accent, children: "\uD83D\uDCAC" }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flexGrow: 1, children: (0, jsx_runtime_1.jsx)(ink_text_input_1.default, { value: state.inputValue, onChange: (value) => setState(prev => ({ ...prev, inputValue: value })), placeholder: "Type your message... (Enter to send, /help for commands)" }) })] }) }), (0, jsx_runtime_1.jsx)(StatusBar_1.StatusBar, { provider: provider, model: model, theme: theme, messageCount: state.messages.length, isTyping: state.isTyping })] }));
};
exports.ChatScreen = ChatScreen;
//# sourceMappingURL=ChatScreen.js.map