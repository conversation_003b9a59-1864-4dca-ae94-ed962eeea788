"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Header = void 0;
const jsx_runtime_1 = require("react/jsx-runtime");
const ink_1 = require("ink");
const Header = ({ title = 'ARIEN AI', subtitle, theme, showLogo = true }) => {
    const primaryColor = theme?.colors.primary || '#00ff9f';
    const textColor = theme?.colors.text || '#ffffff';
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", alignItems: "center", marginBottom: 1, children: [showLogo && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { bold: true, color: primaryColor, children: title }) })), subtitle && ((0, jsx_runtime_1.jsx)(ink_1.Box, { children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: textColor, bold: true, children: subtitle }) })), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, width: 60, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: theme?.colors.muted || '#666666', children: '─'.repeat(60) }) })] }));
};
exports.Header = Header;
//# sourceMappingURL=Header.js.map