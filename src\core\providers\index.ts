/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */

import { BaseLLMProvider, LLMProviderConfig } from './base.js';
import { OpenAIProvider } from './openai.js';
import { AnthropicProvider } from './anthropic.js';
import { GoogleProvider } from './google.js';
import { DeepSeekProvider } from './deepseek.js';
import { AuthConfig } from '../types/index.js';

export class ProviderManager {
  private providers: Map<string, BaseLLMProvider> = new Map();

  createProvider(providerId: string, config: AuthConfig): BaseLLMProvider {
    const providerConfig: LLMProviderConfig = {
      apiKey: config.apiKey,
      accessToken: config.accessToken,
      ...config.additionalConfig
    };

    let provider: BaseLLMProvider;

    switch (providerId) {
      case 'openai':
        provider = new OpenAIProvider(providerConfig);
        break;
      case 'anthropic':
        provider = new AnthropicProvider(providerConfig);
        break;
      case 'google':
        provider = new GoogleProvider(providerConfig);
        break;
      case 'deepseek':
        provider = new DeepSeekProvider(providerConfig);
        break;
      default:
        throw new Error(`Unknown provider: ${providerId}`);
    }

    this.providers.set(providerId, provider);
    return provider;
  }

  getProvider(providerId: string): BaseLLMProvider | undefined {
    return this.providers.get(providerId);
  }

  hasProvider(providerId: string): boolean {
    return this.providers.has(providerId);
  }

  removeProvider(providerId: string): void {
    this.providers.delete(providerId);
  }

  getAllProviders(): Map<string, BaseLLMProvider> {
    return new Map(this.providers);
  }

  async validateProvider(providerId: string): Promise<boolean> {
    const provider = this.getProvider(providerId);
    if (!provider) {
      return false;
    }

    try {
      return await provider.validateConfig();
    } catch (error) {
      return false;
    }
  }
}

export const providerManager = new ProviderManager();

export * from './base.js';
export * from './openai.js';
export * from './anthropic.js';
export * from './google.js';
export * from './deepseek.js';
