# Arien AI CLI - Quick Start Guide

## 🚀 Installation & Setup

### Prerequisites
- Node.js 20 or higher
- npm package manager

### Installation Steps

1. **Install Dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

2. **Build the Project**
   ```bash
   npm run build
   ```

3. **Verify Installation**
   The build should complete successfully without errors.

## 🎯 Project Structure

```
Arien AI CLI/
├── src/                    # Source code
│   ├── cli/               # Frontend (React + Ink)
│   │   ├── components/    # UI components
│   │   ├── screens/       # Application screens
│   │   └── App.tsx        # Main app
│   ├── core/              # Backend logic
│   │   ├── config/        # Configuration
│   │   ├── providers/     # LLM providers
│   │   ├── functions/     # Built-in functions
│   │   └── types/         # TypeScript types
│   └── index.ts           # Entry point
├── dist/                  # Built files
├── package.json           # Dependencies
├── tsconfig.json          # TypeScript config
└── README.md              # Documentation
```

## ✅ What's Included

### LLM Providers (4)
- **DeepSeek**: deepseek-chat, deepseek-reasoner
- **OpenAI**: GPT-4o, GPT-4o Mini, GPT-3.5 Turbo  
- **Google Gemini**: Gemini 2.0 Flash, 1.5 Pro, 1.5 Flash
- **Anthropic**: Claude 3.5 Sonnet, 3.5 Haiku, 3 Opus

### Themes (8)
- Cyberpunk (neon futuristic)
- Matrix (classic green terminal)
- Ocean (calming blues)
- Sunset (warm gradients)
- Minimal (clean monochrome)
- Retro (80s computer vibes)
- Forest (natural greens)
- Galaxy (cosmic purples)

### Built-in Functions (18)
- **System**: Command execution, system info, processes, environment
- **File**: Read, write, list, info, delete operations
- **Web**: Search, fetch, status check, download info
- **Utility**: Calculate, UUID, encode/decode, timestamp, text analysis

## 🔧 Development

### Available Scripts
```bash
npm run build      # Build the project
npm run dev        # Development mode (may have module issues)
npm run clean      # Clean build directory
npm run lint       # Run ESLint
```

### Key Features
- **Modern CLI Interface**: React + Ink terminal UI
- **Multi-Provider Support**: 4 major LLM providers
- **Function Calling**: 18 built-in tools and commands
- **Theme System**: 8 customizable themes with live preview
- **Authentication**: Secure API key and OAuth support
- **Type Safety**: Full TypeScript implementation
- **Modular Architecture**: Clean separation of concerns

## 📝 Configuration

The CLI uses persistent configuration storage for:
- Provider authentication (API keys, OAuth tokens)
- Theme preferences
- System prompts
- Chat history
- User preferences

## 🎨 Customization

### Adding New Themes
Edit `src/core/config/themes.ts` to add new themes with:
- Color palette
- Border styles
- Gradient definitions

### Adding New Functions
Add functions to `src/core/functions/` categories:
- `system.ts` - System operations
- `file.ts` - File operations  
- `web.ts` - Web operations
- `utility.ts` - Utility functions

### Adding New Providers
Create new provider in `src/core/providers/`:
1. Extend `BaseLLMProvider`
2. Implement required methods
3. Add to provider configuration

## 🔒 Security

- API keys stored securely using `conf` package
- Function execution is sandboxed and limited
- File operations have size and path restrictions
- Web requests have timeout and size limits

## 📚 Architecture

The project follows a modular architecture:
- **Frontend**: React + Ink for terminal UI
- **Backend**: TypeScript + Node.js for logic
- **Providers**: Pluggable LLM integrations
- **Functions**: Extensible tool system
- **Config**: Persistent settings management

## 🎉 Ready to Use!

The Arien AI CLI is now ready for:
- ✅ Installation and setup
- ✅ Core functionality
- ✅ Further development
- ✅ Production deployment

For detailed implementation information, see `IMPLEMENTATION_SUMMARY.md`.
