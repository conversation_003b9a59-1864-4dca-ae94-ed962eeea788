#!/usr/bin/env node
/**
 * @license
 * Copyright 2025 Arien LLC
 * License: MIT
 */
import React from 'react';
import { render } from 'ink';
import { App } from './cli/App.js';
// Handle uncaught exceptions gracefully
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Main entry point
async function main() {
    try {
        const { waitUntilExit } = render(React.createElement(App));
        await waitUntilExit();
    }
    catch (error) {
        console.error('Failed to start Arien AI CLI:', error);
        process.exit(1);
    }
}
main();
//# sourceMappingURL=index.js.map