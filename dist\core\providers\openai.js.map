{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../../src/core/providers/openai.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAE,eAAe,EAAqB,MAAM,WAAW,CAAC;AAG/D,MAAM,OAAO,cAAe,SAAQ,eAAe;IACzC,MAAM,CAAS;IAEvB,YAAY,MAAyB;QACnC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;YACvB,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,YAAY,EAAE,MAAM,CAAC,YAAY;SAClC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAI,YAAY;QACd,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CACR,QAAuB,EACvB,KAAa,EACb,OAKC;QAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAE1F,MAAM,aAAa,GAA2C;YAC5D,KAAK;YACL,QAAQ,EAAE,iBAAiB;YAC3B,WAAW,EAAE,OAAO,EAAE,WAAW,IAAI,GAAG;YACxC,UAAU,EAAE,OAAO,EAAE,SAAS;SAC/B,CAAC;QAEF,iDAAiD;QACjD,IAAI,OAAO,EAAE,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvD,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACnD,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;iBAC5B;aACF,CAAC,CAAC,CAAC;YACJ,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC;QACrC,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAC1E,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEnC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;YAC3C,MAAM,aAAa,GAAmB,EAAE,CAAC;YAEzC,wBAAwB;YACxB,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC9B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;oBACjD,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;wBACjC,MAAM,YAAY,GAAiB;4BACjC,EAAE,EAAE,QAAQ,CAAC,EAAE;4BACf,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;4BAC5B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;yBACnD,CAAC;wBAEF,IAAI,CAAC;4BACH,YAAY,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;wBACrE,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,YAAY,CAAC,KAAK,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAC9E,CAAC;wBAED,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACnC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,aAAa;gBACb,KAAK,EAAE;oBACL,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;oBAC/C,YAAY,EAAE,QAAQ,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;oBACpD,WAAW,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY,IAAI,CAAC;iBAC/C;gBACD,KAAK;gBACL,QAAQ,EAAE,IAAI,CAAC,UAAU;aAC1B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;CACF"}